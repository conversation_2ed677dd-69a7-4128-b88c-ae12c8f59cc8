#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版启动脚本 - 解决路径问题
"""

import sys
import os
import webbrowser
import threading
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.absolute()
sys.path.insert(0, str(project_root))

def print_banner():
    """打印启动横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    IoT传感器监控系统                          ║
║                  华为云IoT数据实时监控                        ║
╠══════════════════════════════════════════════════════════════╣
║  🚀 启动模式: 简化集成服务                                    ║
║  📊 后端API: http://127.0.0.1:5000                          ║
║  🌐 前端页面: http://127.0.0.1:5000                          ║
║  📱 响应式设计 | 🎭 动画效果 | ⚡ 实时更新                    ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def create_app():
    """创建Flask应用"""
    from backend.app import create_app
    from flask import send_from_directory, abort
    import os
    
    app = create_app()
    
    # 设置静态文件目录
    frontend_dir = os.path.join(os.path.dirname(__file__), 'frontend')
    
    @app.route('/')
    def index():
        """主页面"""
        return send_from_directory(frontend_dir, 'index.html')
    
    @app.route('/<path:filename>')
    def static_files(filename):
        """静态文件"""
        # 检查是否是API路径
        if filename.startswith('api/') or filename == 'health':
            abort(404)
        
        try:
            return send_from_directory(frontend_dir, filename)
        except:
            abort(404)
    
    return app

def open_browser():
    """延迟打开浏览器"""
    time.sleep(2)
    try:
        webbrowser.open('http://127.0.0.1:5000')
        print("🌐 已在浏览器中打开页面")
    except:
        print("ℹ️ 请手动在浏览器中访问: http://127.0.0.1:5000")

def main():
    """主函数"""
    print_banner()
    
    print("🔍 检查系统...")
    
    # 检查前端文件
    frontend_index = project_root / 'frontend' / 'index.html'
    if not frontend_index.exists():
        print("❌ 前端文件缺失")
        return False
    
    print("✅ 前端文件检查通过")
    
    # 检查后端配置
    try:
        from backend.config import get_config
        config = get_config()
        config.validate()
        print("✅ 后端配置检查通过")
    except Exception as e:
        print(f"❌ 后端配置检查失败: {e}")
        return False
    
    print("\n🚀 启动服务...")
    
    # 启动浏览器线程
    browser_thread = threading.Thread(target=open_browser, daemon=True)
    browser_thread.start()
    
    try:
        app = create_app()
        print("📊 API服务: http://127.0.0.1:5000/api/sensors")
        print("🌐 前端页面: http://127.0.0.1:5000")
        print("💚 健康检查: http://127.0.0.1:5000/health")
        print("\n⏳ 服务启动中...")
        print("💡 按 Ctrl+C 停止服务\n")
        
        app.run(
            host='127.0.0.1',
            port=5000,
            debug=False,
            threaded=True,
            use_reloader=False
        )
        
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 服务启动失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 用户中断，程序退出")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 程序异常退出: {e}")
        sys.exit(1)
