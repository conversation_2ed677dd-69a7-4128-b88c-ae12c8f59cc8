#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试服务器
"""

import sys
import os
import json
import time
import random
from pathlib import Path
from flask import Flask, send_from_directory, jsonify
from flask_cors import CORS

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.absolute()
sys.path.insert(0, str(project_root))

def create_app():
    """创建Flask应用"""
    app = Flask(__name__)
    CORS(app)  # 启用CORS
    
    # 设置静态文件目录
    frontend_dir = str(project_root / 'frontend')
    
    @app.route('/')
    def index():
        """主页面"""
        return send_from_directory(frontend_dir, 'index.html')
    
    @app.route('/test.html')
    def test_page():
        """测试页面"""
        return send_from_directory(frontend_dir, 'test.html')
    
    # 静态文件路由
    @app.route('/css/<filename>')
    def css_files(filename):
        return send_from_directory(os.path.join(frontend_dir, 'css'), filename)
    
    @app.route('/js/<filename>')
    def js_files(filename):
        return send_from_directory(os.path.join(frontend_dir, 'js'), filename)
    
    @app.route('/assets/<path:filename>')
    def assets_files(filename):
        return send_from_directory(os.path.join(frontend_dir, 'assets'), filename)
    
    # API路由
    @app.route('/api/sensors')
    def get_sensors():
        """获取传感器数据 - 使用模拟数据"""
        try:
            # 生成模拟数据
            current_time = time.time()
            
            # 模拟3个节点的数据
            nodes_data = {}
            for i in range(1, 4):
                node_key = f"node_{i}"
                
                # 节点1在线，其他离线
                if i == 1:
                    status = "online"
                    # 生成变化的数据
                    base_temp = 25 + 5 * (0.5 - random.random())
                    base_humidity = 50 + 20 * (0.5 - random.random())
                    base_light = 100 + 50 * (0.5 - random.random())
                    base_smoke = 50 + 30 * (0.5 - random.random())
                else:
                    status = "inactive"
                    # 离线节点数据为0
                    base_temp = 0
                    base_humidity = 0
                    base_light = 0
                    base_smoke = 0
                
                nodes_data[node_key] = {
                    "node_id": str(i),
                    "status": status,
                    "sensors": {
                        "temperature": round(max(0, base_temp), 1),
                        "humidity": round(max(0, min(100, base_humidity)), 1),
                        "light": round(max(0, base_light), 1),
                        "smoke": round(max(0, base_smoke), 1)
                    },
                    "last_update": current_time
                }
            
            return jsonify({
                "success": True,
                "data": {
                    "nodes": nodes_data
                },
                "message": f"成功获取{len(nodes_data)}个节点数据",
                "timestamp": current_time
            })
            
        except Exception as e:
            print(f"API错误: {e}")
            return jsonify({
                "success": False,
                "error": str(e)
            }), 500
    
    @app.route('/health')
    def health():
        """健康检查"""
        return jsonify({
            "status": "healthy",
            "message": "服务正常运行",
            "timestamp": time.time()
        })
    
    return app

def main():
    """主函数"""
    print("🚀 IoT传感器监控系统 - 快速服务器")
    print("=" * 50)
    print("✅ 使用模拟数据")
    print("🌐 启动服务: http://127.0.0.1:5000")
    print("📊 测试页面: http://127.0.0.1:5000/test.html")
    print("💡 按 Ctrl+C 停止服务")
    print("=" * 50)
    
    try:
        app = create_app()
        app.run(
            host='127.0.0.1',
            port=5000,
            debug=True,
            threaded=True
        )
        
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 服务启动失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    main()
