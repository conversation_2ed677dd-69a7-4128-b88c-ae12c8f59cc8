// UI组件逻辑实现
import { CONFIG, Logger } from './config.js';

export class SensorCard {
  constructor(nodeKey, nodeData, index) {
    this.nodeKey = nodeKey;
    this.nodeData = nodeData;
    this.index = index;
    this.element = null;
  }

  render() {
    this.element = document.createElement('div');
    this.element.className = `node-card fade-in-up delay-${Math.min(this.index + 1, 3)}`;
    this.element.innerHTML = this.getCardHTML();
    
    // 添加传感器特定样式
    this.applySensorStyles();
    
    return this.element;
  }

  getCardHTML() {
    const statusText = this.getStatusText(this.nodeData.status);
    const sensorsHTML = this.renderSensors(this.nodeData.sensors);
    
    return `
      <div class="node-header">
        <h3 class="node-title">节点 ${this.nodeData.node_id}</h3>
        <span class="node-status ${this.nodeData.status}">${statusText}</span>
      </div>
      <div class="sensors-grid">
        ${sensorsHTML}
      </div>
      <div class="node-footer">
        <div class="last-update">
          <span class="update-label">最后更新：</span>
          <span class="update-time">${this.formatUpdateTime(this.nodeData.last_update)}</span>
        </div>
      </div>
    `;
  }

  renderSensors(sensors) {
    return Object.entries(sensors).map(([sensorType, value]) => {
      const config = CONFIG.SENSORS[sensorType];
      if (!config) return '';
      
      return `
        <div class="sensor-item ${sensorType}" data-sensor="${sensorType}">
          <div class="sensor-icon">
            ${this.getSensorIcon(sensorType)}
          </div>
          <div class="sensor-info">
            <div class="sensor-name">${config.name}</div>
            <div class="sensor-value" data-value="${value}">
              ${this.formatSensorValue(value, config)}
            </div>
            <div class="sensor-progress">
              ${this.renderProgress(value, config)}
            </div>
          </div>
        </div>
      `;
    }).join('');
  }

  getSensorIcon(sensorType) {
    const iconMap = {
      temperature: `<svg width="20" height="20" viewBox="0 0 24 24" fill="none">
        <path d="M12 2C10.3431 2 9 3.34315 9 5V12.1707C8.37764 12.5825 8 13.2583 8 14C8 15.6569 9.34315 17 11 17C12.6569 17 14 15.6569 14 14C14 13.2583 13.6224 12.5825 13 12.1707V5C13 3.34315 11.6569 2 10 2H12Z" fill="currentColor"/>
        <circle cx="11" cy="14" r="2" fill="currentColor"/>
      </svg>`,
      humidity: `<svg width="20" height="20" viewBox="0 0 24 24" fill="none">
        <path d="M12 2L7.5 8.5C6.5 10 6.5 12 7.5 13.5C8.5 15 10.5 16 12 16C13.5 16 15.5 15 16.5 13.5C17.5 12 17.5 10 16.5 8.5L12 2Z" fill="currentColor"/>
      </svg>`,
      light: `<svg width="20" height="20" viewBox="0 0 24 24" fill="none">
        <circle cx="12" cy="12" r="4" fill="currentColor"/>
        <path d="M12 2V4M12 20V22M4 12H2M22 12H20M19.07 4.93L17.66 6.34M6.34 17.66L4.93 19.07M19.07 19.07L17.66 17.66M6.34 6.34L4.93 4.93" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
      </svg>`,
      smoke: `<svg width="20" height="20" viewBox="0 0 24 24" fill="none">
        <path d="M4 18C4 16.5 5 15.5 6.5 15.5C7.5 15.5 8.5 16 9 16.5C9.5 16 10.5 15.5 11.5 15.5C13 15.5 14 16.5 14 18" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
        <path d="M6 14C6 12.5 7 11.5 8.5 11.5C9.5 11.5 10.5 12 11 12.5C11.5 12 12.5 11.5 13.5 11.5C15 11.5 16 12.5 16 14" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
      </svg>`
    };
    
    return iconMap[sensorType] || '';
  }

  renderProgress(value, config) {
    const percentage = Math.min((value / config.max) * 100, 100);
    
    return `
      <div class="progress-bar">
        <div class="progress-fill" style="width: ${percentage}%; background: ${config.color}"></div>
      </div>
    `;
  }

  formatSensorValue(value, config) {
    if (value === null || value === undefined) {
      return '--';
    }
    
    // 格式化数值显示
    let formattedValue;
    if (typeof value === 'number') {
      if (value % 1 === 0) {
        formattedValue = value.toString();
      } else {
        formattedValue = value.toFixed(1);
      }
    } else {
      formattedValue = value.toString();
    }
    
    return `${formattedValue}${config.unit}`;
  }

  formatUpdateTime(timestamp) {
    if (!timestamp) return '--';
    
    try {
      // 解析华为云时间格式 "20250717T110004Z"
      const year = timestamp.substr(0, 4);
      const month = timestamp.substr(4, 2);
      const day = timestamp.substr(6, 2);
      const hour = timestamp.substr(9, 2);
      const minute = timestamp.substr(11, 2);
      const second = timestamp.substr(13, 2);
      
      const date = new Date(`${year}-${month}-${day}T${hour}:${minute}:${second}Z`);
      return date.toLocaleString('zh-CN');
    } catch (error) {
      Logger.warn('时间格式解析失败', timestamp);
      return timestamp;
    }
  }

  getStatusText(status) {
    const statusMap = {
      'online': '在线',
      'offline': '离线',
      'inactive': '未激活',
      'mock': '模拟'
    };
    return statusMap[status] || status;
  }

  applySensorStyles() {
    if (!this.element) return;
    
    // 为不同传感器类型添加特定样式
    const sensorItems = this.element.querySelectorAll('.sensor-item');
    sensorItems.forEach(item => {
      const sensorType = item.dataset.sensor;
      const config = CONFIG.SENSORS[sensorType];
      if (config) {
        item.style.setProperty('--sensor-color', config.color);
      }
    });
  }

  updateData(newNodeData) {
    this.nodeData = newNodeData;
    
    if (this.element) {
      // 更新状态
      const statusElement = this.element.querySelector('.node-status');
      if (statusElement) {
        statusElement.textContent = this.getStatusText(newNodeData.status);
        statusElement.className = `node-status ${newNodeData.status}`;
      }
      
      // 更新传感器数值
      Object.entries(newNodeData.sensors).forEach(([sensorType, value]) => {
        const config = CONFIG.SENSORS[sensorType];
        if (!config) return;
        
        const valueElement = this.element.querySelector(`[data-sensor="${sensorType}"] .sensor-value`);
        const progressElement = this.element.querySelector(`[data-sensor="${sensorType}"] .progress-fill`);
        
        if (valueElement) {
          valueElement.textContent = this.formatSensorValue(value, config);
          valueElement.dataset.value = value;
        }
        
        if (progressElement) {
          const percentage = Math.min((value / config.max) * 100, 100);
          progressElement.style.width = `${percentage}%`;
        }
      });
      
      // 更新时间
      const updateTimeElement = this.element.querySelector('.update-time');
      if (updateTimeElement) {
        updateTimeElement.textContent = this.formatUpdateTime(newNodeData.last_update);
      }
    }
  }
}

export class NodesGrid {
  constructor(container) {
    this.container = container;
    this.cards = new Map();
  }

  render(nodesData) {
    // 清空容器
    this.container.innerHTML = '';
    this.cards.clear();
    
    // 渲染每个节点卡片
    Object.entries(nodesData).forEach(([nodeKey, nodeData], index) => {
      const card = new SensorCard(nodeKey, nodeData, index);
      const cardElement = card.render();
      
      this.container.appendChild(cardElement);
      this.cards.set(nodeKey, card);
    });
    
    Logger.log(`渲染了${this.cards.size}个节点卡片`);
  }

  updateData(nodesData) {
    Object.entries(nodesData).forEach(([nodeKey, nodeData]) => {
      const card = this.cards.get(nodeKey);
      if (card) {
        card.updateData(nodeData);
      }
    });
    
    Logger.log('更新了节点数据');
  }
}
