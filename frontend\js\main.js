// 应用主逻辑
import { CONFIG, Logger } from './config.js';
import { apiClient } from './api.js';

class IoTApp {
  constructor() {
    this.updateTimer = null;
    this.isLoading = false;
    this.lastUpdateTime = null;
    
    this.init();
  }

  init() {
    Logger.log('IoT应用初始化...');
    
    // 绑定DOM元素
    this.bindElements();
    
    // 初始化事件监听
    this.bindEvents();
    
    // 开始数据更新
    this.startDataUpdates();
    
    Logger.log('IoT应用初始化完成');
  }

  bindElements() {
    this.elements = {
      statusDot: document.getElementById('connectionStatus'),
      statusText: document.getElementById('statusText'),
      lastUpdateTime: document.getElementById('lastUpdateTime'),
      nodesGrid: document.getElementById('nodesGrid'),
      errorSection: document.getElementById('errorSection'),
      retryButton: document.getElementById('retryButton')
    };
  }

  bindEvents() {
    // 重试按钮事件
    if (this.elements.retryButton) {
      this.elements.retryButton.addEventListener('click', () => {
        this.hideError();
        this.loadData();
      });
    }

    // 页面可见性变化事件
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.stopDataUpdates();
      } else {
        this.startDataUpdates();
      }
    });
  }

  async startDataUpdates() {
    Logger.log('开始数据更新循环');
    
    // 立即加载一次数据
    await this.loadData();
    
    // 设置定时更新
    this.updateTimer = setInterval(() => {
      this.loadData();
    }, CONFIG.UPDATE_INTERVAL);
  }

  stopDataUpdates() {
    if (this.updateTimer) {
      clearInterval(this.updateTimer);
      this.updateTimer = null;
      Logger.log('停止数据更新循环');
    }
  }

  async loadData() {
    if (this.isLoading) return;
    
    this.isLoading = true;
    this.updateStatus('连接中...', 'connecting');
    
    try {
      const result = await apiClient.getSensorsData();
      
      if (result.success) {
        this.handleDataSuccess(result.data);
      } else {
        this.handleDataError(result.error);
      }
    } catch (error) {
      this.handleDataError(error.message);
    } finally {
      this.isLoading = false;
    }
  }

  handleDataSuccess(data) {
    Logger.log('数据加载成功', data);
    
    this.updateStatus('已连接', 'online');
    this.updateLastUpdateTime();
    this.hideError();
    
    // 渲染节点数据
    this.renderNodes(data);
  }

  handleDataError(error) {
    Logger.error('数据加载失败', error);
    
    this.updateStatus('连接失败', 'offline');
    this.showError(error);
  }

  updateStatus(text, status) {
    if (this.elements.statusText) {
      this.elements.statusText.textContent = text;
    }
    
    if (this.elements.statusDot) {
      this.elements.statusDot.className = `status-dot ${status}`;
    }
  }

  updateLastUpdateTime() {
    if (this.elements.lastUpdateTime) {
      this.lastUpdateTime = new Date();
      this.elements.lastUpdateTime.textContent = this.lastUpdateTime.toLocaleTimeString();
    }
  }

  renderNodes(data) {
    if (!this.elements.nodesGrid) return;
    
    // 清空加载占位符
    this.elements.nodesGrid.innerHTML = '';
    
    // 渲染每个节点
    Object.entries(data).forEach(([nodeKey, nodeData], index) => {
      const nodeCard = this.createNodeCard(nodeKey, nodeData, index);
      this.elements.nodesGrid.appendChild(nodeCard);
    });
  }

  createNodeCard(nodeKey, nodeData, index) {
    const card = document.createElement('div');
    card.className = `node-card fade-in-up delay-${index + 1}`;
    card.innerHTML = `
      <div class="node-header">
        <h3 class="node-title">节点 ${nodeData.node_id}</h3>
        <span class="node-status ${nodeData.status}">${this.getStatusText(nodeData.status)}</span>
      </div>
      <div class="sensors-grid">
        ${this.renderSensors(nodeData.sensors)}
      </div>
    `;
    return card;
  }

  renderSensors(sensors) {
    return Object.entries(sensors).map(([sensorType, value]) => {
      const config = CONFIG.SENSORS[sensorType];
      if (!config) return '';
      
      return `
        <div class="sensor-item">
          <div class="sensor-icon">${config.icon}</div>
          <div class="sensor-info">
            <div class="sensor-name">${config.name}</div>
            <div class="sensor-value">${value}${config.unit}</div>
          </div>
        </div>
      `;
    }).join('');
  }

  getStatusText(status) {
    const statusMap = {
      'online': '在线',
      'offline': '离线',
      'inactive': '未激活',
      'mock': '模拟'
    };
    return statusMap[status] || status;
  }

  showError(message) {
    if (this.elements.errorSection) {
      const errorMessage = this.elements.errorSection.querySelector('#errorMessage');
      if (errorMessage) {
        errorMessage.textContent = message;
      }
      this.elements.errorSection.style.display = 'block';
    }
  }

  hideError() {
    if (this.elements.errorSection) {
      this.elements.errorSection.style.display = 'none';
    }
  }
}

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
  new IoTApp();
});
