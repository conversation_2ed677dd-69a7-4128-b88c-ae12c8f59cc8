/* 动画效果 */

/* 页面加载动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* 应用动画类 */
.fade-in {
  animation: fadeIn var(--transition-normal) ease-out;
}

.fade-in-up {
  animation: fadeInUp var(--transition-normal) ease-out;
}

/* 延迟动画 */
.delay-1 { animation-delay: 0.1s; }
.delay-2 { animation-delay: 0.2s; }
.delay-3 { animation-delay: 0.3s; }
