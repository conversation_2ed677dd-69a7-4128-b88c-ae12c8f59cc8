# -*- coding: utf-8 -*-
"""
华为云IoT客户端封装模块
重构自src/test.py，提供可复用的IoT设备数据获取功能
"""

import os
import json
import logging
from typing import Dict, List, Optional, Any
from huaweicloudsdkcore.auth.credentials import BasicCredentials
from huaweicloudsdkcore.auth.credentials import DerivedCredentials
from huaweicloudsdkcore.region.region import Region as coreRegion
from huaweicloudsdkcore.exceptions import exceptions
from huaweicloudsdkiotda.v5 import *
from .config import get_config

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class HuaweiIoTClient:
    """华为云IoT客户端封装类"""
    
    def __init__(self, config=None):
        """初始化IoT客户端"""
        self.config = config or get_config()
        self.config.validate()
        self.client = self._create_client()
    
    def _create_client(self) -> IoTDAClient:
        """创建华为云IoT客户端"""
        try:
            credentials = BasicCredentials(
                self.config.HUAWEI_AK, 
                self.config.HUAWEI_SK
            ).with_derived_predicate(DerivedCredentials.get_default_derived_predicate())
            
            client = IoTDAClient.new_builder() \
                .with_credentials(credentials) \
                .with_region(coreRegion(
                    id=self.config.HUAWEI_REGION, 
                    endpoint=self.config.HUAWEI_ENDPOINT
                )) \
                .build()
            
            logger.info("华为云IoT客户端初始化成功")
            return client
            
        except Exception as e:
            logger.error(f"华为云IoT客户端初始化失败: {e}")
            raise
    
    def get_device_shadow(self, device_id: str) -> Optional[Dict[str, Any]]:
        """获取单个设备的影子数据"""
        try:
            request = ShowDeviceShadowRequest()
            request.device_id = device_id
            response = self.client.show_device_shadow(request)
            
            # 转换响应为字典格式
            if hasattr(response, 'to_dict'):
                return response.to_dict()
            else:
                # 如果没有to_dict方法，尝试转换为字符串再解析
                response_str = str(response)
                return {"raw_response": response_str}
                
        except exceptions.ClientRequestException as e:
            logger.error(f"获取设备 {device_id} 影子数据失败: {e.error_msg}")
            return None
        except Exception as e:
            logger.error(f"获取设备 {device_id} 影子数据异常: {e}")
            return None
    
    def get_all_devices_data(self) -> Dict[str, Any]:
        """获取所有配置设备的数据"""
        devices_data = {}

        for i, device_id in enumerate(self.config.DEVICE_IDS, 1):
            # 检查是否为模拟设备
            if device_id.startswith("mock_"):
                logger.info(f"使用模拟数据: {device_id}")
                devices_data[f"node_{i}"] = self._get_mock_data(device_id, i)
            else:
                # 尝试获取真实设备数据
                shadow_data = self.get_device_shadow(device_id)
                if shadow_data:
                    logger.info(f"成功获取设备数据: {device_id}")
                    devices_data[f"node_{i}"] = {
                        "device_id": device_id,
                        "shadow": shadow_data,
                        "sensors": self._extract_sensor_data(shadow_data),
                        "status": "online"
                    }
                else:
                    # 如果获取失败，提供模拟数据
                    logger.warning(f"设备数据获取失败，使用模拟数据: {device_id}")
                    devices_data[f"node_{i}"] = self._get_mock_data(device_id, i)

        return devices_data
    
    def _extract_sensor_data(self, shadow_data: Dict[str, Any]) -> Dict[str, Any]:
        """从影子数据中提取传感器数据"""
        sensors = {
            "temperature": None,
            "humidity": None,
            "light": None,
            "smoke": None
        }

        try:
            # 解析影子数据结构
            if "shadow" in shadow_data and isinstance(shadow_data["shadow"], list):
                for shadow_item in shadow_data["shadow"]:
                    if "reported" in shadow_item and "properties" in shadow_item["reported"]:
                        properties = shadow_item["reported"]["properties"]

                        # 根据实际数据结构提取传感器数据
                        sensors["temperature"] = (
                            properties.get("Temperature") or
                            properties.get("temperature") or
                            properties.get("Node_1_Temperature")
                        )
                        sensors["humidity"] = (
                            properties.get("Humidity") or
                            properties.get("humidity") or
                            properties.get("Node_1_Humidity")
                        )
                        sensors["light"] = (
                            properties.get("Illumination") or
                            properties.get("Light") or
                            properties.get("light") or
                            properties.get("Node_1_Light")
                        )
                        sensors["smoke"] = (
                            properties.get("Smokeconcentration") or
                            properties.get("MQ2") or
                            properties.get("smoke") or
                            properties.get("Node_1_Smog")
                        )

        except Exception as e:
            logger.warning(f"解析传感器数据失败: {e}")

        return sensors
    
    def _get_mock_data(self, device_id: str, node_num: int) -> Dict[str, Any]:
        """生成模拟数据（用于开发和测试）"""
        import random

        # 为不同节点生成不同范围的数据，使数据更真实
        base_temp = 20 + (node_num - 1) * 3  # 节点1:20°C, 节点2:23°C, 节点3:26°C
        base_humidity = 50 + (node_num - 1) * 5  # 不同湿度基准

        return {
            "device_id": device_id,
            "shadow": {"mock": True},
            "sensors": {
                "temperature": round(base_temp + random.uniform(-3, 8), 1),
                "humidity": round(base_humidity + random.uniform(-10, 20), 1),
                "light": round(300 + random.uniform(0, 700), 0),
                "smoke": round(random.uniform(0, 50), 1)  # 正常情况下烟雾浓度较低
            },
            "status": "mock"
        }

# 全局客户端实例
_iot_client = None

def get_iot_client() -> HuaweiIoTClient:
    """获取IoT客户端单例"""
    global _iot_client
    if _iot_client is None:
        _iot_client = HuaweiIoTClient()
    return _iot_client
