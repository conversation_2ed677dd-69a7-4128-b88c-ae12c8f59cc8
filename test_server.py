#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试服务器 - 最简单的启动方式
"""

import sys
import os
import webbrowser
import threading
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.absolute()
sys.path.insert(0, str(project_root))

def create_test_app():
    """创建测试Flask应用"""
    from flask import Flask, send_from_directory, jsonify
    from backend.iot_client import get_iot_client
    
    app = Flask(__name__)
    
    # 设置静态文件目录
    frontend_dir = str(project_root / 'frontend')
    
    @app.route('/')
    def index():
        """主页面"""
        return send_from_directory(frontend_dir, 'index.html')
    
    # 静态文件路由
    @app.route('/css/<filename>')
    def css_files(filename):
        return send_from_directory(os.path.join(frontend_dir, 'css'), filename)
    
    @app.route('/js/<filename>')
    def js_files(filename):
        return send_from_directory(os.path.join(frontend_dir, 'js'), filename)
    
    @app.route('/assets/<path:filename>')
    def assets_files(filename):
        return send_from_directory(os.path.join(frontend_dir, 'assets'), filename)
    
    # API路由
    @app.route('/api/sensors')
    def get_sensors():
        """获取传感器数据"""
        try:
            client = get_iot_client()
            data = client.get_all_devices_data()
            
            return jsonify({
                "success": True,
                "data": {
                    "nodes": data
                },
                "message": f"成功获取{len(data)}个节点数据"
            })
        except Exception as e:
            return jsonify({
                "success": False,
                "error": str(e)
            }), 500
    
    @app.route('/health')
    def health():
        """健康检查"""
        return jsonify({
            "status": "healthy",
            "message": "服务正常运行"
        })
    
    return app

def open_browser():
    """延迟打开浏览器"""
    time.sleep(2)
    try:
        webbrowser.open('http://127.0.0.1:5000')
        print("🌐 已在浏览器中打开页面")
    except:
        print("ℹ️ 请手动在浏览器中访问: http://127.0.0.1:5000")

def main():
    """主函数"""
    print("🚀 IoT传感器监控系统 - 测试服务器")
    print("=" * 50)
    
    # 检查文件
    frontend_index = project_root / 'frontend' / 'index.html'
    if not frontend_index.exists():
        print("❌ 前端文件缺失")
        return False
    
    print("✅ 文件检查通过")
    print("🌐 启动服务: http://127.0.0.1:5000")
    print("💡 按 Ctrl+C 停止服务")
    print("=" * 50)
    
    # 启动浏览器线程
    browser_thread = threading.Thread(target=open_browser, daemon=True)
    browser_thread.start()
    
    try:
        app = create_test_app()
        app.run(
            host='127.0.0.1',
            port=5000,
            debug=False,
            threaded=True
        )
        
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 服务启动失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    main()
