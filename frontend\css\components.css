/* 组件样式 */

/* 应用容器 */
.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 头部样式 */
.app-header {
  background: var(--bg-card);
  border-bottom: 1px solid var(--border-light);
  padding: var(--spacing-lg) var(--spacing-md);
  box-shadow: var(--shadow-small);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.app-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--status-offline);
  transition: background var(--transition-fast);
}

.status-dot.online { background: var(--status-online); }
.status-dot.warning { background: var(--status-warning); }

/* 主内容区域 */
.main-content {
  flex: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-xl) var(--spacing-md);
  width: 100%;
}

/* 节点网格 */
.nodes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
  margin-top: var(--spacing-xl);
}

/* 加载占位符 */
.loading-placeholder {
  grid-column: 1 / -1;
  text-align: center;
  padding: var(--spacing-2xl);
  color: var(--text-secondary);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border-light);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto var(--spacing-md);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 节点卡片样式 */
.node-card {
  background: var(--bg-card);
  border-radius: var(--radius-large);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-card);
  border: 1px solid var(--border-light);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.node-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-large);
}

.node-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--border-light);
}

.node-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0;
}

.node-status {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-small);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.node-status.online {
  background: var(--success-color);
  color: white;
}

.node-status.offline {
  background: var(--error-color);
  color: white;
}

.node-status.inactive {
  background: var(--text-tertiary);
  color: var(--text-primary);
}

.node-status.mock {
  background: var(--secondary-color);
  color: white;
}

/* 传感器网格 */
.sensors-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-md);
}

/* 传感器项目 */
.sensor-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--bg-secondary);
  border-radius: var(--radius-medium);
  transition: all var(--transition-fast);
  position: relative;
}

.sensor-item:hover {
  background: var(--bg-tertiary);
}

.sensor-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-medium);
  background: var(--bg-card);
  box-shadow: var(--shadow-small);
  flex-shrink: 0;
}

.sensor-icon svg {
  width: 20px;
  height: 20px;
}

.sensor-info {
  flex: 1;
  min-width: 0;
}

.sensor-name {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
  font-weight: var(--font-weight-medium);
}

.sensor-value {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  line-height: 1;
}

/* 传感器特定颜色 */
.sensor-item.temperature .sensor-icon {
  background: var(--sensor-temperature);
  color: white;
}

.sensor-item.humidity .sensor-icon {
  background: var(--sensor-humidity);
  color: white;
}

.sensor-item.light .sensor-icon {
  background: var(--sensor-light);
  color: var(--text-primary);
}

.sensor-item.smoke .sensor-icon {
  background: var(--sensor-smoke);
  color: white;
}

/* 进度条样式 */
.sensor-progress {
  margin-top: var(--spacing-xs);
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: var(--border-light);
  border-radius: 2px;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: var(--primary-color);
  border-radius: 2px;
  transition: width var(--transition-normal);
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.3) 50%, transparent 100%);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* 节点底部信息 */
.node-footer {
  margin-top: var(--spacing-lg);
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--border-light);
}

.last-update {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
}

.update-label {
  font-weight: var(--font-weight-medium);
}

.update-time {
  color: var(--text-tertiary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: var(--spacing-md);
    text-align: center;
  }

  .nodes-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .main-content {
    padding: var(--spacing-lg) var(--spacing-md);
  }

  .sensors-grid {
    grid-template-columns: 1fr;
  }

  .node-card {
    padding: var(--spacing-md);
  }
}
