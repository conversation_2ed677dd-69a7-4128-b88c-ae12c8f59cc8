// 应用主逻辑
import { CONFIG, Logger } from './config.js';
import { apiClient } from './api.js';
import { NodesGrid } from './components.js';

class IoTApp {
  constructor() {
    this.updateTimer = null;
    this.isLoading = false;
    this.lastUpdateTime = null;
    this.nodesGrid = null;
    this.errorCount = 0;
    this.lastData = null;

    this.init();
  }

  init() {
    Logger.log('IoT应用初始化...');
    
    // 绑定DOM元素
    this.bindElements();

    // 初始化组件
    this.initComponents();

    // 初始化事件监听
    this.bindEvents();

    // 开始数据更新
    this.startDataUpdates();
    
    Logger.log('IoT应用初始化完成');
  }

  bindElements() {
    this.elements = {
      statusDot: document.getElementById('connectionStatus'),
      statusText: document.getElementById('statusText'),
      lastUpdateTime: document.getElementById('lastUpdateTime'),
      nodesGrid: document.getElementById('nodesGrid'),
      errorSection: document.getElementById('errorSection'),
      retryButton: document.getElementById('retryButton')
    };
  }

  initComponents() {
    if (this.elements.nodesGrid) {
      this.nodesGrid = new NodesGrid(this.elements.nodesGrid);
      Logger.log('节点网格组件初始化完成');
    }
  }

  bindEvents() {
    // 重试按钮事件
    if (this.elements.retryButton) {
      this.elements.retryButton.addEventListener('click', () => {
        this.hideError();
        this.loadData();
      });
    }

    // 页面可见性变化事件
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.stopDataUpdates();
      } else {
        this.startDataUpdates();
      }
    });
  }

  async startDataUpdates() {
    Logger.log('开始数据更新循环');
    
    // 立即加载一次数据
    await this.loadData();
    
    // 设置定时更新
    this.updateTimer = setInterval(() => {
      this.loadData();
    }, CONFIG.UPDATE_INTERVAL);
  }

  stopDataUpdates() {
    if (this.updateTimer) {
      clearInterval(this.updateTimer);
      this.updateTimer = null;
      Logger.log('停止数据更新循环');
    }
  }

  async loadData() {
    if (this.isLoading) return;

    this.isLoading = true;

    // 更新连接状态
    const connectionStatus = apiClient.getConnectionStatus();
    this.updateConnectionStatus(connectionStatus);

    try {
      const result = await apiClient.getSensorsData();

      if (result.success) {
        this.handleDataSuccess(result.data);
        this.errorCount = 0; // 重置错误计数
      } else {
        this.handleDataError(result.error);
      }
    } catch (error) {
      this.handleDataError(error.message);
    } finally {
      this.isLoading = false;
    }
  }

  handleDataSuccess(data) {
    Logger.log('数据加载成功', data);

    this.lastData = data;
    this.updateStatus('已连接', 'online');
    this.updateLastUpdateTime();
    this.hideError();

    // 渲染或更新节点数据
    if (this.nodesGrid && this.nodesGrid.cards.size > 0) {
      // 如果已有卡片，更新数据
      this.nodesGrid.updateData(data.nodes || data);
    } else {
      // 首次渲染
      this.renderNodes(data);
    }
  }

  handleDataError(error) {
    this.errorCount++;
    Logger.error(`数据加载失败 (第${this.errorCount}次)`, error);

    // 根据错误次数调整状态
    if (this.errorCount >= 3) {
      this.updateStatus('连接失败', 'offline');
      this.showError(`连续${this.errorCount}次获取数据失败: ${error}`);
    } else {
      this.updateStatus('连接异常', 'warning');
      // 轻微错误不显示错误界面，只在状态栏提示
    }

    // 如果有上次成功的数据，继续显示
    if (this.lastData && this.errorCount < 5) {
      Logger.log('使用上次成功的数据');
      // 可以在这里添加数据过期提示
    }
  }

  updateStatus(text, status) {
    if (this.elements.statusText) {
      this.elements.statusText.textContent = text;
    }

    if (this.elements.statusDot) {
      this.elements.statusDot.className = `status-dot ${status}`;
    }
  }

  updateConnectionStatus(connectionStatus) {
    const statusMap = {
      'online': { text: '已连接', status: 'online' },
      'connecting': { text: '连接中...', status: 'connecting' },
      'warning': { text: '连接不稳定', status: 'warning' },
      'error': { text: '连接异常', status: 'warning' },
      'offline': { text: '网络离线', status: 'offline' }
    };

    const statusInfo = statusMap[connectionStatus.status] || statusMap['connecting'];
    this.updateStatus(statusInfo.text, statusInfo.status);

    // 添加详细的连接信息到状态文本
    if (connectionStatus.timeSinceLastSuccess && connectionStatus.timeSinceLastSuccess > 30000) {
      const minutes = Math.floor(connectionStatus.timeSinceLastSuccess / 60000);
      if (minutes > 0) {
        this.updateStatus(`${statusInfo.text} (${minutes}分钟前)`, statusInfo.status);
      }
    }
  }

  updateLastUpdateTime() {
    if (this.elements.lastUpdateTime) {
      this.lastUpdateTime = new Date();
      this.elements.lastUpdateTime.textContent = this.lastUpdateTime.toLocaleTimeString();
    }
  }

  renderNodes(data) {
    if (!this.nodesGrid) return;

    // 使用NodesGrid组件渲染
    this.nodesGrid.render(data.nodes || data);
  }



  showError(message) {
    if (this.elements.errorSection) {
      const errorMessage = this.elements.errorSection.querySelector('#errorMessage');
      if (errorMessage) {
        errorMessage.textContent = message;
      }
      this.elements.errorSection.style.display = 'block';
    }
  }

  hideError() {
    if (this.elements.errorSection) {
      this.elements.errorSection.style.display = 'none';
    }
  }
}

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
  new IoTApp();
});
