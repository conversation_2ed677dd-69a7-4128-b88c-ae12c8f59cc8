<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IoT传感器监控 - 测试版</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            margin: 0;
            padding: 20px;
            background: #f2f2f7;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            background: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: 10px;
        }
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #ff3b30;
        }
        .status-dot.online {
            background: #34c759;
        }
        .nodes-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        .node-card {
            background: white;
            padding: 20px;
            border-radius: 16px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.08);
        }
        .node-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e5e5ea;
        }
        .node-status {
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
        }
        .node-status.online {
            background: #34c759;
            color: white;
        }
        .node-status.inactive {
            background: #c7c7cc;
            color: #1c1c1e;
        }
        .sensors-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }
        .sensor-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            background: #f2f2f7;
            border-radius: 12px;
        }
        .sensor-icon {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 12px;
            font-size: 20px;
        }
        .sensor-icon.temperature {
            background: #ff6b6b;
        }
        .sensor-icon.humidity {
            background: #4ecdc4;
        }
        .sensor-icon.light {
            background: #ffe66d;
        }
        .sensor-icon.smoke {
            background: #95a5a6;
        }
        .sensor-info {
            flex: 1;
        }
        .sensor-name {
            font-size: 14px;
            color: #8e8e93;
            margin-bottom: 4px;
        }
        .sensor-value {
            font-size: 18px;
            font-weight: 600;
            color: #1c1c1e;
        }
        .loading {
            text-align: center;
            padding: 40px;
            color: #8e8e93;
        }
        .error {
            text-align: center;
            padding: 40px;
            color: #ff3b30;
        }

        /* 图表样式 */
        .charts-section {
            margin-top: 30px;
        }

        .chart-container {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.08);
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e5e5ea;
        }

        .chart-title {
            font-size: 18px;
            font-weight: 600;
            color: #1c1c1e;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .chart-controls {
            display: flex;
            gap: 10px;
        }

        .time-range-btn {
            padding: 6px 12px;
            border: 1px solid #e5e5ea;
            border-radius: 8px;
            background: white;
            color: #1c1c1e;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .time-range-btn:hover {
            background: #f2f2f7;
        }

        .time-range-btn.active {
            background: #007aff;
            color: white;
            border-color: #007aff;
        }

        .chart-canvas {
            width: 100%;
            height: 300px;
            border-radius: 12px;
            background: #f8f9fa;
        }

        .chart-legend {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 15px;
            flex-wrap: wrap;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 12px;
            color: #8e8e93;
        }

        .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 2px;
        }

        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 IoT传感器监控</h1>
            <div class="status">
                <span class="status-dot" id="statusDot"></span>
                <span id="statusText">连接中...</span>
            </div>
        </div>

        <h2>实时数据概览</h2>
        <div id="lastUpdate">最后更新：--</div>

        <div class="nodes-grid" id="nodesGrid">
            <div class="loading">正在加载传感器数据...</div>
        </div>

        <!-- 图表区域 -->
        <div class="charts-section">
            <h2>📈 历史数据趋势</h2>
            <div class="charts-grid" id="chartsGrid">
                <!-- 图表将通过JavaScript动态生成 -->
            </div>
        </div>
    </div>

    <script>
        // 数据存储
        const dataHistory = {
            temperature: { node_1: [], node_2: [], node_3: [] },
            humidity: { node_1: [], node_2: [], node_3: [] },
            light: { node_1: [], node_2: [], node_3: [] },
            smoke: { node_1: [], node_2: [], node_3: [] }
        };

        const maxDataPoints = 50; // 最大数据点数量
        let charts = {}; // 存储图表实例

        // 图表配置
        const chartConfigs = {
            temperature: {
                name: '温度',
                unit: '°C',
                icon: '🌡️',
                color: '#ff6b6b',
                min: 0,
                max: 50
            },
            humidity: {
                name: '湿度',
                unit: '%',
                icon: '💧',
                color: '#4ecdc4',
                min: 0,
                max: 100
            },
            light: {
                name: '光照',
                unit: ' lux',
                icon: '☀️',
                color: '#ffe66d',
                min: 0,
                max: 1000
            },
            smoke: {
                name: '烟雾',
                unit: ' ppm',
                icon: '💨',
                color: '#95a5a6',
                min: 0,
                max: 500
            }
        };

        const nodeColors = {
            node_1: '#007aff',
            node_2: '#34c759',
            node_3: '#ff9500'
        };

        // 简化版JavaScript
        async function loadData() {
            try {
                console.log('开始获取数据...');
                const response = await fetch('/api/sensors');
                const result = await response.json();
                
                console.log('API响应:', result);
                
                if (result.success) {
                    updateStatus('已连接', true);
                    renderNodes(result.data.nodes);
                    updateDataHistory(result.data.nodes);
                    updateCharts();
                    updateLastUpdate();
                } else {
                    throw new Error(result.error || '数据获取失败');
                }
            } catch (error) {
                console.error('数据加载失败:', error);
                updateStatus('连接失败', false);
                showError(error.message);
            }
        }

        function updateStatus(text, isOnline) {
            document.getElementById('statusText').textContent = text;
            const dot = document.getElementById('statusDot');
            if (isOnline) {
                dot.classList.add('online');
            } else {
                dot.classList.remove('online');
            }
        }

        function updateLastUpdate() {
            document.getElementById('lastUpdate').textContent = 
                '最后更新：' + new Date().toLocaleTimeString();
        }

        function renderNodes(nodes) {
            const grid = document.getElementById('nodesGrid');
            grid.innerHTML = '';

            Object.entries(nodes).forEach(([nodeKey, nodeData]) => {
                const card = createNodeCard(nodeKey, nodeData);
                grid.appendChild(card);
            });
        }

        function createNodeCard(nodeKey, nodeData) {
            const card = document.createElement('div');
            card.className = 'node-card';
            
            const sensors = nodeData.sensors || {};
            const status = nodeData.status || 'unknown';
            
            card.innerHTML = `
                <div class="node-header">
                    <h3>节点 ${nodeData.node_id || nodeKey.replace('node_', '')}</h3>
                    <span class="node-status ${status}">${getStatusText(status)}</span>
                </div>
                <div class="sensors-grid">
                    <div class="sensor-item">
                        <div class="sensor-icon temperature">🌡️</div>
                        <div class="sensor-info">
                            <div class="sensor-name">温度</div>
                            <div class="sensor-value">${sensors.temperature || 0}°C</div>
                        </div>
                    </div>
                    <div class="sensor-item">
                        <div class="sensor-icon humidity">💧</div>
                        <div class="sensor-info">
                            <div class="sensor-name">湿度</div>
                            <div class="sensor-value">${sensors.humidity || 0}%</div>
                        </div>
                    </div>
                    <div class="sensor-item">
                        <div class="sensor-icon light">☀️</div>
                        <div class="sensor-info">
                            <div class="sensor-name">光照</div>
                            <div class="sensor-value">${sensors.light || 0} lux</div>
                        </div>
                    </div>
                    <div class="sensor-item">
                        <div class="sensor-icon smoke">💨</div>
                        <div class="sensor-info">
                            <div class="sensor-name">烟雾</div>
                            <div class="sensor-value">${sensors.smoke || 0} ppm</div>
                        </div>
                    </div>
                </div>
            `;
            
            return card;
        }

        function getStatusText(status) {
            const statusMap = {
                'online': '在线',
                'offline': '离线',
                'inactive': '未激活',
                'mock': '模拟'
            };
            return statusMap[status] || status;
        }

        function showError(message) {
            const grid = document.getElementById('nodesGrid');
            grid.innerHTML = `<div class="error">❌ ${message}</div>`;
        }

        // 更新数据历史
        function updateDataHistory(nodes) {
            const timestamp = new Date();

            Object.entries(nodes).forEach(([nodeKey, nodeData]) => {
                const sensors = nodeData.sensors || {};

                Object.entries(sensors).forEach(([sensorType, value]) => {
                    if (dataHistory[sensorType] && dataHistory[sensorType][nodeKey]) {
                        const nodeHistory = dataHistory[sensorType][nodeKey];

                        // 添加新数据点
                        nodeHistory.push({
                            time: timestamp,
                            value: parseFloat(value) || 0
                        });

                        // 保持最大数据点数量
                        if (nodeHistory.length > maxDataPoints) {
                            nodeHistory.shift();
                        }

                        console.log(`更新 ${sensorType} ${nodeKey}: ${value} (历史点数: ${nodeHistory.length})`);
                    }
                });
            });
        }

        // 生成初始历史数据
        function generateInitialData() {
            const now = new Date();
            const pointCount = 20; // 生成20个初始数据点

            console.log('生成初始历史数据...');

            for (let i = pointCount; i >= 0; i--) {
                const timestamp = new Date(now.getTime() - i * 30000); // 每30秒一个点

                // 为每个传感器类型和节点生成数据
                Object.keys(dataHistory).forEach(sensorType => {
                    Object.keys(dataHistory[sensorType]).forEach(nodeKey => {
                        let value = 0;

                        // 只为node_1生成有意义的数据
                        if (nodeKey === 'node_1') {
                            switch(sensorType) {
                                case 'temperature':
                                    value = 25 + 5 * Math.sin(i * 0.1) + 2 * (Math.random() - 0.5);
                                    break;
                                case 'humidity':
                                    value = 50 + 15 * Math.cos(i * 0.08) + 5 * (Math.random() - 0.5);
                                    break;
                                case 'light':
                                    value = 200 + 100 * Math.sin(i * 0.05) + 20 * (Math.random() - 0.5);
                                    break;
                                case 'smoke':
                                    value = 100 + 50 * Math.sin(i * 0.12) + 10 * (Math.random() - 0.5);
                                    break;
                            }
                            value = Math.max(0, value); // 确保非负值
                        }

                        dataHistory[sensorType][nodeKey].push({
                            time: timestamp,
                            value: parseFloat(value.toFixed(1))
                        });
                    });
                });
            }

            console.log('初始数据生成完成');
        }

        // 初始化图表
        function initCharts() {
            const chartsGrid = document.getElementById('chartsGrid');
            chartsGrid.innerHTML = '';

            Object.entries(chartConfigs).forEach(([sensorType, config]) => {
                const chartContainer = createChartContainer(sensorType, config);
                chartsGrid.appendChild(chartContainer);

                // 创建Canvas图表
                const canvas = chartContainer.querySelector('.chart-canvas');
                charts[sensorType] = new SimpleChart(canvas, config, sensorType);
            });
        }

        // 创建图表容器
        function createChartContainer(sensorType, config) {
            const container = document.createElement('div');
            container.className = 'chart-container';
            container.innerHTML = `
                <div class="chart-header">
                    <div class="chart-title">
                        ${config.icon} ${config.name}趋势图
                    </div>
                    <div class="chart-controls">
                        <button class="time-range-btn active" data-range="10">10分钟</button>
                        <button class="time-range-btn" data-range="30">30分钟</button>
                        <button class="time-range-btn" data-range="60">1小时</button>
                    </div>
                </div>
                <canvas class="chart-canvas" width="800" height="300"></canvas>
                <div class="chart-legend">
                    <div class="legend-item">
                        <div class="legend-color" style="background: ${nodeColors.node_1}"></div>
                        <span>节点1</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: ${nodeColors.node_2}"></div>
                        <span>节点2</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: ${nodeColors.node_3}"></div>
                        <span>节点3</span>
                    </div>
                </div>
            `;

            // 绑定时间范围按钮事件
            const buttons = container.querySelectorAll('.time-range-btn');
            buttons.forEach(btn => {
                btn.addEventListener('click', () => {
                    buttons.forEach(b => b.classList.remove('active'));
                    btn.classList.add('active');

                    const range = parseInt(btn.dataset.range);
                    if (charts[sensorType]) {
                        charts[sensorType].setTimeRange(range);
                    }
                });
            });

            return container;
        }

        // 更新所有图表
        function updateCharts() {
            Object.keys(charts).forEach(sensorType => {
                if (charts[sensorType]) {
                    charts[sensorType].updateData(dataHistory[sensorType]);
                }
            });
        }

        // 简单图表类
        class SimpleChart {
            constructor(canvas, config, sensorType) {
                this.canvas = canvas;
                this.ctx = canvas.getContext('2d');
                this.config = config;
                this.sensorType = sensorType;
                this.timeRange = 10; // 默认10分钟
                this.data = {};

                // 设置Canvas尺寸
                this.setupCanvas();
            }

            setupCanvas() {
                const rect = this.canvas.getBoundingClientRect();
                const dpr = window.devicePixelRatio || 1;

                this.canvas.width = rect.width * dpr;
                this.canvas.height = rect.height * dpr;

                this.ctx.scale(dpr, dpr);
                this.canvas.style.width = rect.width + 'px';
                this.canvas.style.height = rect.height + 'px';
            }

            setTimeRange(minutes) {
                this.timeRange = minutes;
                this.updateData(this.data);
            }

            updateData(data) {
                this.data = data;
                this.draw();
            }

            draw() {
                const ctx = this.ctx;
                const canvas = this.canvas;
                const width = canvas.width / (window.devicePixelRatio || 1);
                const height = canvas.height / (window.devicePixelRatio || 1);

                // 清空画布
                ctx.clearRect(0, 0, width, height);

                // 绘制背景
                ctx.fillStyle = '#ffffff';
                ctx.fillRect(0, 0, width, height);

                // 绘制网格
                this.drawGrid(ctx, width, height);

                // 绘制数据线
                this.drawDataLines(ctx, width, height);

                // 绘制坐标轴标签
                this.drawLabels(ctx, width, height);
            }

            drawGrid(ctx, width, height) {
                const padding = 60;
                const gridColor = '#e5e5ea';

                ctx.strokeStyle = gridColor;
                ctx.lineWidth = 1;

                // 垂直网格线
                for (let i = 0; i <= 10; i++) {
                    const x = padding + (width - padding * 2) * i / 10;
                    ctx.beginPath();
                    ctx.moveTo(x, padding);
                    ctx.lineTo(x, height - padding);
                    ctx.stroke();
                }

                // 水平网格线
                for (let i = 0; i <= 5; i++) {
                    const y = padding + (height - padding * 2) * i / 5;
                    ctx.beginPath();
                    ctx.moveTo(padding, y);
                    ctx.lineTo(width - padding, y);
                    ctx.stroke();
                }
            }

            drawDataLines(ctx, width, height) {
                const padding = 60;
                const chartWidth = width - padding * 2;
                const chartHeight = height - padding * 2;

                const now = new Date();
                const timeRangeMs = this.timeRange * 60 * 1000;
                const startTime = new Date(now.getTime() - timeRangeMs);

                Object.entries(this.data).forEach(([nodeKey, nodeData]) => {
                    if (!nodeData || nodeData.length === 0) return;

                    // 过滤时间范围内的数据
                    const filteredData = nodeData.filter(point =>
                        point.time >= startTime && point.time <= now
                    );

                    if (filteredData.length < 1) return;

                    // 设置线条样式
                    ctx.strokeStyle = nodeColors[nodeKey];
                    ctx.lineWidth = 2;
                    ctx.beginPath();

                    filteredData.forEach((point, index) => {
                        const x = padding + chartWidth * (point.time - startTime) / timeRangeMs;
                        const y = padding + chartHeight * (1 - (point.value - this.config.min) / (this.config.max - this.config.min));

                        if (index === 0) {
                            ctx.moveTo(x, y);
                        } else {
                            ctx.lineTo(x, y);
                        }
                    });

                    ctx.stroke();

                    // 绘制数据点
                    ctx.fillStyle = nodeColors[nodeKey];
                    filteredData.forEach(point => {
                        const x = padding + chartWidth * (point.time - startTime) / timeRangeMs;
                        const y = padding + chartHeight * (1 - (point.value - this.config.min) / (this.config.max - this.config.min));

                        ctx.beginPath();
                        ctx.arc(x, y, 3, 0, Math.PI * 2);
                        ctx.fill();
                    });
                });
            }

            drawLabels(ctx, width, height) {
                const padding = 60;

                ctx.fillStyle = '#8e8e93';
                ctx.font = '12px -apple-system, BlinkMacSystemFont, sans-serif';
                ctx.textAlign = 'center';

                // Y轴标签（数值）
                for (let i = 0; i <= 5; i++) {
                    const value = this.config.min + (this.config.max - this.config.min) * (1 - i / 5);
                    const y = padding + (height - padding * 2) * i / 5;

                    ctx.textAlign = 'right';
                    ctx.fillText(value.toFixed(0) + this.config.unit, padding - 10, y + 4);
                }

                // X轴标签（时间）
                const now = new Date();
                for (let i = 0; i <= 5; i++) {
                    const timeOffset = this.timeRange * 60 * 1000 * (1 - i / 5);
                    const time = new Date(now.getTime() - timeOffset);
                    const x = padding + (width - padding * 2) * i / 5;

                    ctx.textAlign = 'center';
                    ctx.fillText(time.toLocaleTimeString().slice(0, 5), x, height - padding + 20);
                }
            }
        }

        // 页面加载完成后开始获取数据
        document.addEventListener('DOMContentLoaded', () => {
            console.log('页面加载完成，开始获取数据');

            // 1. 生成初始历史数据
            generateInitialData();

            // 2. 初始化图表
            initCharts();

            // 3. 更新图表显示初始数据
            updateCharts();

            // 4. 开始获取实时数据
            loadData();

            // 5. 每10秒更新一次数据
            setInterval(loadData, 10000);
        });
    </script>
</body>
</html>
