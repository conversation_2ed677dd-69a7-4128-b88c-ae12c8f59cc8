#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IoT传感器监控系统 - 统一启动脚本
提供一键启动方案，集成前后端服务
"""

import sys
import os
import time
import threading
import subprocess
import webbrowser
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def print_banner():
    """打印启动横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    IoT传感器监控系统                          ║
║                  华为云IoT数据实时监控                        ║
╠══════════════════════════════════════════════════════════════╣
║  🚀 启动模式: 集成服务                                        ║
║  📊 后端API: http://127.0.0.1:5000                          ║
║  🌐 前端页面: http://127.0.0.1:5000                          ║
║  📱 响应式设计 | 🎭 动画效果 | ⚡ 实时更新                    ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_dependencies():
    """检查依赖是否安装"""
    print("🔍 检查依赖...")
    
    try:
        import flask
        import flask_cors
        from huaweicloudsdkcore.auth.credentials import BasicCredentials
        print("✅ 所有依赖已安装")
        return True
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请运行: pip install -r requirements.txt")
        return False

def check_config():
    """检查配置文件"""
    print("🔧 检查配置...")
    
    env_file = project_root / ".env"
    if not env_file.exists():
        print("❌ 未找到.env配置文件")
        print("请复制.env.template为.env并填入正确的华为云配置")
        return False
    
    # 检查关键配置
    try:
        from backend.config import get_config
        config = get_config()
        config.validate()
        print("✅ 配置文件验证通过")
        return True
    except Exception as e:
        print(f"❌ 配置验证失败: {e}")
        return False

def test_iot_connection():
    """测试IoT连接"""
    print("📡 测试华为云IoT连接...")
    
    try:
        from backend.iot_client import get_iot_client
        client = get_iot_client()
        data = client.get_all_devices_data()
        
        if data:
            online_nodes = sum(1 for node in data.values() if node.get('status') == 'online')
            print(f"✅ IoT连接成功，获取到{len(data)}个节点，{online_nodes}个在线")
            return True
        else:
            print("⚠️ IoT连接成功但未获取到数据")
            return True  # 仍然允许启动，可能使用模拟数据
    except Exception as e:
        print(f"⚠️ IoT连接测试失败: {e}")
        print("系统将使用模拟数据运行")
        return True  # 允许启动，使用模拟数据

def create_integrated_app():
    """创建集成的Flask应用（包含静态文件服务）"""
    from backend.app import create_app
    from flask import send_from_directory, send_file

    app = create_app()

    # 确保使用正确的项目根路径
    global project_root

    # 修改后端的根路由为API信息页面
    @app.route('/api')
    def api_info():
        """API信息页面"""
        from backend.utils.response import api_success
        return api_success({
            "name": "IoT传感器数据API",
            "version": "1.0.0",
            "description": "华为云IoT传感器数据监控API服务",
            "endpoints": {
                "sensors": "/api/sensors",
                "node_data": "/api/sensors/<node_id>",
                "stats": "/api/sensors/stats",
                "health": "/health"
            }
        })

    # 添加前端页面路由
    @app.route('/')
    def frontend_index():
        """前端主页面"""
        frontend_path = project_root / 'frontend' / 'index.html'
        return send_file(str(frontend_path))

    @app.route('/frontend/<path:filename>')
    def frontend_static(filename):
        """前端静态文件"""
        frontend_dir = project_root / 'frontend'
        return send_from_directory(str(frontend_dir), filename)

    @app.route('/favicon.ico')
    def favicon():
        """网站图标"""
        assets_dir = project_root / 'frontend' / 'assets'
        try:
            return send_from_directory(str(assets_dir), 'favicon.ico',
                                     mimetype='image/vnd.microsoft.icon')
        except:
            # 如果没有favicon，返回404
            from flask import abort
            abort(404)

    return app

def start_integrated_server():
    """启动集成服务器"""
    print("🚀 启动集成服务器...")
    
    try:
        app = create_integrated_app()
        config = app.config
        
        print(f"📊 API服务: http://127.0.0.1:5000/api/sensors")
        print(f"🌐 前端页面: http://127.0.0.1:5000")
        print(f"💚 健康检查: http://127.0.0.1:5000/health")
        print("\n⏳ 服务启动中...")
        
        # 延迟打开浏览器
        def open_browser():
            time.sleep(2)
            try:
                webbrowser.open('http://127.0.0.1:5000')
                print("🌐 已在浏览器中打开页面")
            except:
                print("ℹ️ 请手动在浏览器中访问: http://127.0.0.1:5000")
        
        browser_thread = threading.Thread(target=open_browser, daemon=True)
        browser_thread.start()
        
        # 启动Flask服务
        app.run(
            host='127.0.0.1',
            port=5000,
            debug=False,
            threaded=True,
            use_reloader=False
        )
        
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 服务启动失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print_banner()
    
    # 预检查
    if not check_dependencies():
        return False
    
    if not check_config():
        return False
    
    test_iot_connection()
    
    print("\n" + "="*60)
    print("🎉 系统检查完成，准备启动服务...")
    print("💡 提示: 按 Ctrl+C 停止服务")
    print("="*60 + "\n")
    
    # 启动集成服务
    return start_integrated_server()

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 用户中断，程序退出")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 程序异常退出: {e}")
        sys.exit(1)
