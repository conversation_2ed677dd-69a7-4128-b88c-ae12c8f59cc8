# 华为云IoT传感器数据展示系统

现代化的静态前端网页，用于显示华为云IoT三个节点的传感器数据（温度、湿度、光照、烟雾浓度）。

## 项目特性

- 🎨 苹果设计风格的现代化UI
- 📱 响应式设计，适配各种设备
- ⚡ 实时数据更新
- 🎭 平滑动画效果
- 🔧 纯HTML/CSS/JS实现，无框架依赖
- 🌐 本地服务器部署支持

## 项目结构

```
├── backend/                 # 后端代码
│   ├── api/                # API路由
│   ├── utils/              # 工具模块
│   ├── config.py           # 配置管理
│   └── iot_client.py       # 华为云IoT客户端
├── frontend/               # 前端代码
│   ├── css/               # 样式文件
│   ├── js/                # JavaScript文件
│   ├── assets/            # 静态资源
│   └── index.html         # 主页面
├── src/                   # 原始代码（保留）
├── .env                   # 环境变量配置
├── .env.template          # 环境变量模板
├── requirements.txt       # Python依赖
└── README.md             # 项目说明
```

## 快速开始

### 1. 环境准备

确保已安装Python 3.8+

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 配置环境变量

复制 `.env.template` 为 `.env` 并填入您的华为云IoT配置：

```bash
cp .env.template .env
```

编辑 `.env` 文件，填入正确的华为云AK/SK等配置。

### 4. 测试IoT连接

```bash
python -c "from backend.iot_client import get_iot_client; client = get_iot_client(); print('IoT客户端初始化成功')"
```

## 开发说明

### 配置管理

- `backend/config.py`: 统一的配置管理，支持开发/生产环境切换
- 环境变量通过 `.env` 文件管理
- 支持多设备ID配置

### IoT客户端

- `backend/iot_client.py`: 华为云IoT客户端封装
- 支持设备影子数据获取
- 自动解析传感器数据
- 提供模拟数据支持（开发阶段）

## 下一步开发

1. 后端API服务开发
2. 前端基础架构搭建
3. 传感器数据可视化组件
4. 实时数据更新系统
5. 动画效果优化
6. 部署配置完善

## 技术栈

- **后端**: Python, Flask, 华为云IoT SDK
- **前端**: HTML5, CSS3, JavaScript ES6+
- **部署**: 本地服务器(127.0.0.1)

## 许可证

MIT License
