<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="华为云IoT传感器数据实时监控系统">
    <title>IoT传感器监控 - 华为云</title>
    
    <!-- CSS样式文件 -->
    <link rel="stylesheet" href="css/variables.css">
    <link rel="stylesheet" href="css/base.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/animations.css">
    
    <!-- 预加载关键资源 -->
    <link rel="preload" href="js/config.js" as="script">
    <link rel="preload" href="js/api.js" as="script">
</head>
<body>
    <!-- 主容器 -->
    <div class="app-container">
        <!-- 头部区域 -->
        <header class="app-header">
            <div class="header-content">
                <h1 class="app-title">
                    <span class="title-icon">📊</span>
                    IoT传感器监控
                </h1>
                <div class="status-indicator">
                    <span class="status-dot" id="connectionStatus"></span>
                    <span class="status-text" id="statusText">连接中...</span>
                </div>
            </div>
        </header>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 数据概览卡片 -->
            <section class="overview-section">
                <div class="section-header">
                    <h2 class="section-title">实时数据概览</h2>
                    <div class="last-update">
                        <span>最后更新：</span>
                        <time id="lastUpdateTime">--</time>
                    </div>
                </div>
            </section>

            <!-- 传感器节点网格 -->
            <section class="nodes-grid" id="nodesGrid">
                <!-- 节点卡片将通过JavaScript动态生成 -->
                <div class="loading-placeholder">
                    <div class="loading-spinner"></div>
                    <p>正在加载传感器数据...</p>
                </div>
            </section>

            <!-- 错误提示区域 -->
            <section class="error-section" id="errorSection" style="display: none;">
                <div class="error-card">
                    <div class="error-icon">⚠️</div>
                    <h3 class="error-title">数据获取失败</h3>
                    <p class="error-message" id="errorMessage">请检查网络连接或稍后重试</p>
                    <button class="retry-button" id="retryButton">重新加载</button>
                </div>
            </section>
        </main>

        <!-- 底部信息 -->
        <footer class="app-footer">
            <div class="footer-content">
                <p class="footer-text">
                    华为云IoT传感器监控系统 | 
                    <span class="footer-version">v1.0.0</span>
                </p>
                <div class="footer-links">
                    <a href="#" class="footer-link">帮助</a>
                    <a href="#" class="footer-link">关于</a>
                </div>
            </div>
        </footer>
    </div>

    <!-- 简化版JavaScript -->
    <script>
        // 配置
        const CONFIG = {
            API_BASE_URL: 'http://127.0.0.1:5000',
            UPDATE_INTERVAL: 10000,
            SENSORS: {
                temperature: { name: '温度', unit: '°C', icon: '🌡️', color: '#FF6B6B' },
                humidity: { name: '湿度', unit: '%', icon: '💧', color: '#4ECDC4' },
                light: { name: '光照', unit: ' lux', icon: '☀️', color: '#FFE66D' },
                smoke: { name: '烟雾', unit: ' ppm', icon: '💨', color: '#95A5A6' }
            }
        };

        // 应用主逻辑
        class IoTApp {
            constructor() {
                this.updateTimer = null;
                this.isLoading = false;
                this.init();
            }

            init() {
                this.bindElements();
                this.bindEvents();
                this.startDataUpdates();
            }

            bindElements() {
                this.elements = {
                    statusDot: document.getElementById('connectionStatus'),
                    statusText: document.getElementById('statusText'),
                    lastUpdateTime: document.getElementById('lastUpdateTime'),
                    nodesGrid: document.getElementById('nodesGrid'),
                    errorSection: document.getElementById('errorSection'),
                    retryButton: document.getElementById('retryButton')
                };
            }

            bindEvents() {
                if (this.elements.retryButton) {
                    this.elements.retryButton.addEventListener('click', () => {
                        this.hideError();
                        this.loadData();
                    });
                }

                document.addEventListener('visibilitychange', () => {
                    if (document.hidden) {
                        this.stopDataUpdates();
                    } else {
                        this.startDataUpdates();
                    }
                });
            }

            async startDataUpdates() {
                await this.loadData();
                this.updateTimer = setInterval(() => {
                    this.loadData();
                }, CONFIG.UPDATE_INTERVAL);
            }

            stopDataUpdates() {
                if (this.updateTimer) {
                    clearInterval(this.updateTimer);
                    this.updateTimer = null;
                }
            }

            async loadData() {
                if (this.isLoading) return;

                this.isLoading = true;
                this.updateStatus('连接中...', 'connecting');

                try {
                    const response = await fetch('/api/sensors');
                    const result = await response.json();

                    if (result.success) {
                        this.handleDataSuccess(result.data);
                    } else {
                        this.handleDataError(result.error);
                    }
                } catch (error) {
                    this.handleDataError(error.message);
                } finally {
                    this.isLoading = false;
                }
            }

            handleDataSuccess(data) {
                this.updateStatus('已连接', 'online');
                this.updateLastUpdateTime();
                this.hideError();
                this.renderNodes(data.nodes || data);
            }

            handleDataError(error) {
                this.updateStatus('连接失败', 'offline');
                this.showError(error);
            }

            updateStatus(text, status) {
                if (this.elements.statusText) {
                    this.elements.statusText.textContent = text;
                }

                if (this.elements.statusDot) {
                    this.elements.statusDot.className = `status-dot ${status}`;
                }
            }

            updateLastUpdateTime() {
                if (this.elements.lastUpdateTime) {
                    this.elements.lastUpdateTime.textContent = new Date().toLocaleTimeString();
                }
            }

            renderNodes(data) {
                if (!this.elements.nodesGrid) return;

                this.elements.nodesGrid.innerHTML = '';

                Object.entries(data).forEach(([nodeKey, nodeData], index) => {
                    const nodeCard = this.createNodeCard(nodeKey, nodeData, index);
                    this.elements.nodesGrid.appendChild(nodeCard);
                });
            }

            createNodeCard(nodeKey, nodeData, index) {
                const card = document.createElement('div');
                card.className = `node-card fade-in-up delay-${index + 1}`;
                card.innerHTML = `
                    <div class="node-header">
                        <h3 class="node-title">节点 ${nodeData.node_id}</h3>
                        <span class="node-status ${nodeData.status}">${this.getStatusText(nodeData.status)}</span>
                    </div>
                    <div class="sensors-grid">
                        ${this.renderSensors(nodeData.sensors)}
                    </div>
                `;
                return card;
            }

            renderSensors(sensors) {
                return Object.entries(sensors).map(([sensorType, value]) => {
                    const config = CONFIG.SENSORS[sensorType];
                    if (!config) return '';

                    return `
                        <div class="sensor-item ${sensorType}">
                            <div class="sensor-icon">${config.icon}</div>
                            <div class="sensor-info">
                                <div class="sensor-name">${config.name}</div>
                                <div class="sensor-value">${value}${config.unit}</div>
                            </div>
                        </div>
                    `;
                }).join('');
            }

            getStatusText(status) {
                const statusMap = {
                    'online': '在线',
                    'offline': '离线',
                    'inactive': '未激活',
                    'mock': '模拟'
                };
                return statusMap[status] || status;
            }

            showError(message) {
                if (this.elements.errorSection) {
                    const errorMessage = this.elements.errorSection.querySelector('#errorMessage');
                    if (errorMessage) {
                        errorMessage.textContent = message;
                    }
                    this.elements.errorSection.style.display = 'block';
                }
            }

            hideError() {
                if (this.elements.errorSection) {
                    this.elements.errorSection.style.display = 'none';
                }
            }
        }

        // 页面加载完成后初始化应用
        document.addEventListener('DOMContentLoaded', () => {
            new IoTApp();
        });
    </script>
</body>
</html>
