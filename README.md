# 华为云IoT传感器数据展示系统

现代化的静态前端网页，用于显示华为云IoT三个节点的传感器数据（温度、湿度、光照、烟雾浓度）。

## 🌟 项目特性

- 🎨 **苹果设计风格** - 现代化UI，圆角阴影，优雅动效
- 📱 **响应式设计** - 完美适配手机、平板、桌面设备
- ⚡ **实时数据更新** - 10秒自动刷新，智能错误处理
- 🎭 **平滑动画效果** - 页面加载、数值变化、交互反馈动画
- 🔧 **纯前端实现** - HTML/CSS/JS，无框架依赖
- 🌐 **一键部署** - 集成服务器，127.0.0.1本地访问
- 📊 **数据可视化** - 进度条、图标、状态指示器
- 🔄 **智能重试** - 网络异常自动重试，离线数据缓存

## 🏗️ 项目架构

```
├── backend/                 # 后端服务
│   ├── api/                # RESTful API接口
│   │   └── sensors.py      # 传感器数据API
│   ├── utils/              # 工具模块
│   │   ├── cache.py        # 数据缓存
│   │   └── response.py     # 统一响应格式
│   ├── app.py              # Flask应用
│   ├── config.py           # 配置管理
│   └── iot_client.py       # 华为云IoT客户端
├── frontend/               # 前端页面
│   ├── css/               # 样式文件
│   │   ├── variables.css   # CSS变量（主题色彩）
│   │   ├── base.css        # 基础样式
│   │   ├── components.css  # 组件样式
│   │   └── animations.css  # 动画效果
│   ├── js/                # JavaScript模块
│   │   ├── config.js       # 前端配置
│   │   ├── api.js          # API调用
│   │   ├── components.js   # UI组件
│   │   ├── animations.js   # 动画控制
│   │   └── main.js         # 应用主逻辑
│   ├── assets/            # 静态资源
│   │   └── icons/         # SVG图标
│   └── index.html         # 主页面
├── start.py               # 一键启动脚本
├── .env                   # 环境变量配置
├── requirements.txt       # Python依赖
└── README.md             # 项目文档
```

## 🚀 快速开始

### 1. 环境要求

- **Python 3.8+** - 推荐使用 Python 3.9 或更高版本
- **现代浏览器** - Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- **网络连接** - 需要访问华为云IoT服务

### 2. 安装依赖

```bash
# 安装Python依赖
pip install -r requirements.txt

# 或者使用国内镜像加速
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

### 3. 配置环境变量

复制环境变量模板并填入华为云IoT配置：

```bash
cp .env.template .env
```

编辑 `.env` 文件，填入您的华为云IoT配置：

```env
# 华为云IoT配置（必填）
HUAWEI_AK=your_access_key_here
HUAWEI_SK=your_secret_key_here
HUAWEI_ENDPOINT=your_iot_endpoint
HUAWEI_REGION=cn-north-4

# API服务配置（可选）
API_HOST=127.0.0.1
API_PORT=5000
CACHE_TIMEOUT=30
```

### 4. 一键启动

```bash
# 生产模式 - 集成服务器
python start.py

# 开发模式 - 分离式服务器
python dev_start.py
```

启动后自动打开浏览器访问 `http://127.0.0.1:5000`

### 5. 验证部署

系统启动后，您应该看到：

- ✅ **依赖检查通过** - 所有Python包正确安装
- ✅ **配置验证通过** - 华为云AK/SK配置正确
- ✅ **IoT连接成功** - 能够获取设备数据
- ✅ **服务启动成功** - API和前端服务正常运行

如果遇到问题，请查看 [故障排除](#🔍-故障排除) 部分。

## 📊 功能说明

### 实时数据监控

- **3个IoT节点** - 显示所有节点的在线状态
- **4种传感器** - 温度、湿度、光照、烟雾浓度
- **实时更新** - 每10秒自动刷新数据
- **状态指示** - 在线/离线/未激活状态显示

### 数据可视化

- **进度条** - 直观显示传感器数值范围
- **图标系统** - SVG图标表示不同传感器类型
- **颜色编码** - 不同传感器使用专属颜色
- **数值动画** - 数据变化时的平滑过渡效果

### 用户体验

- **响应式布局** - 自适应手机、平板、桌面
- **加载动画** - 页面加载时的优雅过渡
- **交互反馈** - 悬停、点击的即时反馈
- **错误处理** - 网络异常时的友好提示

## 🔧 开发模式

### 分离式启动（开发调试）

启动后端API服务：
```bash
python backend/app.py
```

启动前端开发服务器：
```bash
python -m http.server 8000
```

访问：
- API服务：http://127.0.0.1:5000
- 前端页面：http://127.0.0.1:8000/frontend/index.html

### API接口

- `GET /` - 系统信息
- `GET /health` - 健康检查
- `GET /api/sensors` - 获取所有传感器数据
- `GET /api/sensors/<node_id>` - 获取指定节点数据
- `GET /api/sensors/stats` - 获取统计信息
- `DELETE /api/sensors/cache` - 清空缓存

## 🎨 设计规范

### 色彩系统

- **主色调**: #007AFF (苹果蓝)
- **成功色**: #34C759 (绿色)
- **警告色**: #FF9500 (橙色)
- **错误色**: #FF3B30 (红色)
- **背景色**: #F2F2F7 (浅灰)

### 动画规范

- **动画时长**: 300ms (标准), 400ms (数值变化)
- **缓动函数**: ease-out-quart
- **延迟动画**: 100ms-500ms 渐进式
- **硬件加速**: 使用 transform 和 opacity

## 🔍 故障排除

### 常见问题

1. **依赖安装失败**
   ```bash
   pip install --upgrade pip
   pip install -r requirements.txt --no-cache-dir
   ```

2. **华为云连接失败**
   - 检查 `.env` 文件中的 AK/SK 配置
   - 确认网络连接正常
   - 验证华为云IoT设备状态

3. **页面无法访问**
   - 确认防火墙允许 127.0.0.1:5000
   - 检查端口是否被占用
   - 尝试使用 `localhost:5000`

4. **数据不更新**
   - 检查浏览器控制台错误信息
   - 验证API接口 `/api/sensors` 返回数据
   - 清空浏览器缓存

### 性能优化

- **缓存机制**: 后端30秒缓存，减少API调用
- **动画优化**: CSS3硬件加速，避免重排重绘
- **资源压缩**: 生产环境建议压缩CSS/JS文件
- **CDN加速**: 可将静态资源部署到CDN

## 📈 系统监控

访问 `http://127.0.0.1:5000/api/sensors/stats` 查看系统状态：

```json
{
  "success": true,
  "data": {
    "cache": {
      "total_items": 1,
      "valid_items": 1,
      "cache_timeout": 30
    },
    "nodes_status": {
      "node_1": {"status": "online", "sensors_count": 4},
      "node_2": {"status": "inactive", "sensors_count": 4},
      "node_3": {"status": "inactive", "sensors_count": 4}
    }
  }
}
```

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📞 技术支持

如有问题或建议，请提交 [Issue](../../issues) 或联系开发团队。

---

**🎉 享受现代化的IoT数据监控体验！**
