<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IoT传感器监控 - 测试版</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            margin: 0;
            padding: 20px;
            background: #f2f2f7;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            background: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: 10px;
        }
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #ff3b30;
        }
        .status-dot.online {
            background: #34c759;
        }
        .nodes-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        .node-card {
            background: white;
            padding: 20px;
            border-radius: 16px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.08);
        }
        .node-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e5e5ea;
        }
        .node-status {
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
        }
        .node-status.online {
            background: #34c759;
            color: white;
        }
        .node-status.inactive {
            background: #c7c7cc;
            color: #1c1c1e;
        }
        .sensors-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }
        .sensor-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            background: #f2f2f7;
            border-radius: 12px;
        }
        .sensor-icon {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 12px;
            font-size: 20px;
        }
        .sensor-icon.temperature {
            background: #ff6b6b;
        }
        .sensor-icon.humidity {
            background: #4ecdc4;
        }
        .sensor-icon.light {
            background: #ffe66d;
        }
        .sensor-icon.smoke {
            background: #95a5a6;
        }
        .sensor-info {
            flex: 1;
        }
        .sensor-name {
            font-size: 14px;
            color: #8e8e93;
            margin-bottom: 4px;
        }
        .sensor-value {
            font-size: 18px;
            font-weight: 600;
            color: #1c1c1e;
        }
        .loading {
            text-align: center;
            padding: 40px;
            color: #8e8e93;
        }
        .error {
            text-align: center;
            padding: 40px;
            color: #ff3b30;
        }

        /* 图表样式 */
        .charts-section {
            margin-top: 40px;
            padding: 0 10px;
        }

        .chart-container {
            background: white;
            border-radius: 16px;
            padding: 24px;
            margin: 25px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.08);
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            position: relative;
            z-index: 1;
            min-height: 420px;
        }

        .chart-container:hover {
            box-shadow: 0 6px 25px rgba(0,0,0,0.12);
            z-index: 2;
        }

        .chart-container.active-chart {
            border-color: #007aff;
            box-shadow: 0 6px 25px rgba(0, 122, 255, 0.25);
            z-index: 3;
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e5e5ea;
        }

        .chart-title {
            font-size: 18px;
            font-weight: 600;
            color: #1c1c1e;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .chart-controls {
            display: flex;
            gap: 10px;
        }

        .time-range-btn {
            padding: 6px 12px;
            border: 1px solid #e5e5ea;
            border-radius: 8px;
            background: white;
            color: #1c1c1e;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .time-range-btn:hover {
            background: #f2f2f7;
        }

        .time-range-btn.active {
            background: #007aff;
            color: white;
            border-color: #007aff;
        }

        .chart-canvas {
            width: 100%;
            height: 300px;
            border-radius: 12px;
            background: #f8f9fa;
            display: block;
            position: relative;
        }

        .chart-legend {
            display: flex;
            justify-content: center;
            gap: 24px;
            margin-top: 20px;
            padding-top: 16px;
            border-top: 1px solid #f2f2f7;
            flex-wrap: wrap;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 12px;
            color: #8e8e93;
        }

        .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 2px;
        }

        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(520px, 1fr));
            gap: 40px 30px; /* 垂直间距40px，水平间距30px */
            position: relative;
            padding: 20px 10px;
        }

        @media (max-width: 1200px) {
            .charts-grid {
                grid-template-columns: 1fr;
                gap: 35px 25px;
            }
        }

        @media (max-width: 768px) {
            .charts-grid {
                gap: 30px 20px;
                padding: 15px 5px;
            }
        }

        /* 确保图表容器有足够的隔离空间 */
        .chart-container::before {
            content: '';
            position: absolute;
            top: -15px;
            left: -15px;
            right: -15px;
            bottom: -15px;
            z-index: -1;
            pointer-events: none;
        }

        /* 数据清除功能样式 */
        .data-controls {
            background: white;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.08);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 20px;
        }

        .data-info {
            display: flex;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .data-stat {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 14px;
            color: #8e8e93;
        }

        .data-stat-value {
            font-weight: 600;
            color: #1c1c1e;
        }

        .clear-controls {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .clear-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .clear-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .clear-btn:active {
            transform: translateY(0);
        }

        .clear-btn.clear-all {
            background: #ff3b30;
            color: white;
        }

        .clear-btn.clear-all:hover {
            background: #d70015;
        }

        .clear-btn.clear-single {
            background: #ff9500;
            color: white;
        }

        .clear-btn.clear-single:hover {
            background: #e6850e;
        }

        .clear-btn.export-data {
            background: #007aff;
            color: white;
        }

        .clear-btn.export-data:hover {
            background: #0056cc;
        }

        .clear-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
            box-shadow: none !important;
        }

        /* 确认对话框样式 */
        .confirm-dialog {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .confirm-dialog.show {
            opacity: 1;
            visibility: visible;
        }

        .confirm-content {
            background: white;
            border-radius: 16px;
            padding: 24px;
            max-width: 400px;
            width: 90%;
            text-align: center;
            transform: scale(0.9);
            transition: transform 0.3s ease;
        }

        .confirm-dialog.show .confirm-content {
            transform: scale(1);
        }

        .confirm-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }

        .confirm-title {
            font-size: 18px;
            font-weight: 600;
            color: #1c1c1e;
            margin-bottom: 8px;
        }

        .confirm-message {
            font-size: 14px;
            color: #8e8e93;
            margin-bottom: 24px;
            line-height: 1.4;
        }

        .confirm-buttons {
            display: flex;
            gap: 12px;
            justify-content: center;
        }

        .confirm-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            min-width: 80px;
        }

        .confirm-btn.cancel {
            background: #f2f2f7;
            color: #1c1c1e;
        }

        .confirm-btn.cancel:hover {
            background: #e5e5ea;
        }

        .confirm-btn.confirm {
            background: #ff3b30;
            color: white;
        }

        .confirm-btn.confirm:hover {
            background: #d70015;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 IoT传感器监控</h1>
            <div class="status">
                <span class="status-dot" id="statusDot"></span>
                <span id="statusText">连接中...</span>
            </div>
        </div>

        <h2>实时数据概览</h2>
        <div id="lastUpdate">最后更新：--</div>

        <div class="nodes-grid" id="nodesGrid">
            <div class="loading">正在加载传感器数据...</div>
        </div>

        <!-- 图表区域 -->
        <div class="charts-section">
            <h2>📈 历史数据趋势</h2>

            <!-- 数据控制面板 -->
            <div class="data-controls">
                <div class="data-info">
                    <div class="data-stat">
                        <span>📊</span>
                        <span>数据点数：</span>
                        <span class="data-stat-value" id="dataPointsCount">0</span>
                    </div>
                    <div class="data-stat">
                        <span>⏱️</span>
                        <span>记录时长：</span>
                        <span class="data-stat-value" id="recordDuration">0分钟</span>
                    </div>
                    <div class="data-stat">
                        <span>💾</span>
                        <span>内存使用：</span>
                        <span class="data-stat-value" id="memoryUsage">0KB</span>
                    </div>
                </div>

                <div class="clear-controls">
                    <button class="clear-btn export-data" id="exportDataBtn">
                        <span>📤</span>
                        <span>导出数据</span>
                    </button>
                    <button class="clear-btn clear-single" id="clearCurrentBtn">
                        <span>🧹</span>
                        <span>清除当前</span>
                    </button>
                    <button class="clear-btn clear-all" id="clearAllBtn">
                        <span>🗑️</span>
                        <span>清空所有</span>
                    </button>
                </div>
            </div>

            <div class="charts-grid" id="chartsGrid">
                <!-- 图表将通过JavaScript动态生成 -->
            </div>
        </div>
    </div>

    <!-- 确认对话框 -->
    <div class="confirm-dialog" id="confirmDialog">
        <div class="confirm-content">
            <div class="confirm-icon" id="confirmIcon">⚠️</div>
            <div class="confirm-title" id="confirmTitle">确认操作</div>
            <div class="confirm-message" id="confirmMessage">此操作不可撤销，确定要继续吗？</div>
            <div class="confirm-buttons">
                <button class="confirm-btn cancel" id="confirmCancel">取消</button>
                <button class="confirm-btn confirm" id="confirmOk">确定</button>
            </div>
        </div>
    </div>

    <script>
        // 数据存储
        const dataHistory = {
            temperature: { node_1: [], node_2: [], node_3: [] },
            humidity: { node_1: [], node_2: [], node_3: [] },
            light: { node_1: [], node_2: [], node_3: [] },
            smoke: { node_1: [], node_2: [], node_3: [] }
        };

        const maxDataPoints = 50; // 最大数据点数量
        let charts = {}; // 存储图表实例
        let currentActiveChart = null; // 当前活跃的图表（用于单独清除）

        // 图表配置 - 所有Y轴从0开始
        const chartConfigs = {
            temperature: {
                name: '温度',
                unit: '°C',
                icon: '🌡️',
                color: '#ff6b6b',
                min: 0,
                max: 50
            },
            humidity: {
                name: '湿度',
                unit: '%',
                icon: '💧',
                color: '#4ecdc4',
                min: 0,
                max: 100
            },
            light: {
                name: '光照',
                unit: ' lux',
                icon: '☀️',
                color: '#ffe66d',
                min: 0,
                max: 500
            },
            smoke: {
                name: '烟雾',
                unit: ' ppm',
                icon: '💨',
                color: '#95a5a6',
                min: 0,
                max: 300
            }
        };

        const nodeColors = {
            node_1: '#007aff',
            node_2: '#34c759',
            node_3: '#ff9500'
        };

        // 简化版JavaScript
        async function loadData() {
            try {
                console.log('开始获取数据...');
                const response = await fetch('/api/sensors');
                const result = await response.json();
                
                console.log('API响应:', result);
                
                if (result.success) {
                    updateStatus('已连接', true);
                    renderNodes(result.data.nodes);
                    updateDataHistory(result.data.nodes);
                    updateCharts();
                    updateDataStats();
                    updateLastUpdate();
                } else {
                    throw new Error(result.error || '数据获取失败');
                }
            } catch (error) {
                console.error('数据加载失败:', error);
                updateStatus('连接失败', false);
                showError(error.message);
            }
        }

        function updateStatus(text, isOnline) {
            document.getElementById('statusText').textContent = text;
            const dot = document.getElementById('statusDot');
            if (isOnline) {
                dot.classList.add('online');
            } else {
                dot.classList.remove('online');
            }
        }

        function updateLastUpdate() {
            document.getElementById('lastUpdate').textContent = 
                '最后更新：' + new Date().toLocaleTimeString();
        }

        function renderNodes(nodes) {
            const grid = document.getElementById('nodesGrid');
            grid.innerHTML = '';

            Object.entries(nodes).forEach(([nodeKey, nodeData]) => {
                const card = createNodeCard(nodeKey, nodeData);
                grid.appendChild(card);
            });
        }

        function createNodeCard(nodeKey, nodeData) {
            const card = document.createElement('div');
            card.className = 'node-card';
            
            const sensors = nodeData.sensors || {};
            const status = nodeData.status || 'unknown';
            
            card.innerHTML = `
                <div class="node-header">
                    <h3>节点 ${nodeData.node_id || nodeKey.replace('node_', '')}</h3>
                    <span class="node-status ${status}">${getStatusText(status)}</span>
                </div>
                <div class="sensors-grid">
                    <div class="sensor-item">
                        <div class="sensor-icon temperature">🌡️</div>
                        <div class="sensor-info">
                            <div class="sensor-name">温度</div>
                            <div class="sensor-value">${sensors.temperature || 0}°C</div>
                        </div>
                    </div>
                    <div class="sensor-item">
                        <div class="sensor-icon humidity">💧</div>
                        <div class="sensor-info">
                            <div class="sensor-name">湿度</div>
                            <div class="sensor-value">${sensors.humidity || 0}%</div>
                        </div>
                    </div>
                    <div class="sensor-item">
                        <div class="sensor-icon light">☀️</div>
                        <div class="sensor-info">
                            <div class="sensor-name">光照</div>
                            <div class="sensor-value">${sensors.light || 0} lux</div>
                        </div>
                    </div>
                    <div class="sensor-item">
                        <div class="sensor-icon smoke">💨</div>
                        <div class="sensor-info">
                            <div class="sensor-name">烟雾</div>
                            <div class="sensor-value">${sensors.smoke || 0} ppm</div>
                        </div>
                    </div>
                </div>
            `;
            
            return card;
        }

        function getStatusText(status) {
            const statusMap = {
                'online': '在线',
                'offline': '离线',
                'inactive': '未激活',
                'mock': '模拟'
            };
            return statusMap[status] || status;
        }

        function showError(message) {
            const grid = document.getElementById('nodesGrid');
            grid.innerHTML = `<div class="error">❌ ${message}</div>`;
        }

        // 更新数据历史
        function updateDataHistory(nodes) {
            const timestamp = new Date();

            Object.entries(nodes).forEach(([nodeKey, nodeData]) => {
                const sensors = nodeData.sensors || {};

                Object.entries(sensors).forEach(([sensorType, value]) => {
                    if (dataHistory[sensorType] && dataHistory[sensorType][nodeKey]) {
                        const nodeHistory = dataHistory[sensorType][nodeKey];

                        // 添加新数据点
                        nodeHistory.push({
                            time: timestamp,
                            value: parseFloat(value) || 0
                        });

                        // 保持最大数据点数量
                        if (nodeHistory.length > maxDataPoints) {
                            nodeHistory.shift();
                        }

                        console.log(`更新 ${sensorType} ${nodeKey}: ${value} (历史点数: ${nodeHistory.length})`);
                    }
                });
            });
        }

        // 生成初始历史数据
        function generateInitialData() {
            const now = new Date();
            const pointCount = 20; // 生成20个初始数据点

            console.log('生成初始历史数据...');

            for (let i = pointCount; i >= 0; i--) {
                const timestamp = new Date(now.getTime() - i * 30000); // 每30秒一个点

                // 为每个传感器类型和节点生成数据
                Object.keys(dataHistory).forEach(sensorType => {
                    Object.keys(dataHistory[sensorType]).forEach(nodeKey => {
                        let value = 0;

                        // 只为node_1生成有意义的数据
                        if (nodeKey === 'node_1') {
                            switch(sensorType) {
                                case 'temperature':
                                    value = 25 + 8 * Math.sin(i * 0.1) + 3 * (Math.random() - 0.5);
                                    value = Math.max(18, Math.min(35, value)); // 限制在18-35°C范围
                                    break;
                                case 'humidity':
                                    value = 50 + 20 * Math.cos(i * 0.08) + 8 * (Math.random() - 0.5);
                                    value = Math.max(30, Math.min(80, value)); // 限制在30-80%范围
                                    break;
                                case 'light':
                                    value = 250 + 120 * Math.sin(i * 0.05) + 30 * (Math.random() - 0.5);
                                    value = Math.max(100, Math.min(450, value)); // 限制在100-450 lux范围
                                    break;
                                case 'smoke':
                                    value = 120 + 60 * Math.sin(i * 0.12) + 20 * (Math.random() - 0.5);
                                    value = Math.max(50, Math.min(250, value)); // 限制在50-250 ppm范围
                                    break;
                            }
                        }

                        dataHistory[sensorType][nodeKey].push({
                            time: timestamp,
                            value: parseFloat(value.toFixed(1))
                        });
                    });
                });
            }

            console.log('初始数据生成完成');
        }

        // 更新数据统计信息
        function updateDataStats() {
            let totalPoints = 0;
            let oldestTime = null;
            let newestTime = null;

            // 计算总数据点数和时间范围
            Object.values(dataHistory).forEach(sensorData => {
                Object.values(sensorData).forEach(nodeData => {
                    totalPoints += nodeData.length;

                    if (nodeData.length > 0) {
                        const firstTime = nodeData[0].time;
                        const lastTime = nodeData[nodeData.length - 1].time;

                        if (!oldestTime || firstTime < oldestTime) {
                            oldestTime = firstTime;
                        }
                        if (!newestTime || lastTime > newestTime) {
                            newestTime = lastTime;
                        }
                    }
                });
            });

            // 更新显示
            document.getElementById('dataPointsCount').textContent = totalPoints;

            if (oldestTime && newestTime) {
                const durationMs = newestTime - oldestTime;
                const durationMinutes = Math.round(durationMs / (1000 * 60));
                document.getElementById('recordDuration').textContent = durationMinutes + '分钟';
            } else {
                document.getElementById('recordDuration').textContent = '0分钟';
            }

            // 估算内存使用（每个数据点约50字节）
            const memoryKB = Math.round(totalPoints * 50 / 1024);
            document.getElementById('memoryUsage').textContent = memoryKB + 'KB';
        }

        // 清空所有数据
        function clearAllData() {
            showConfirmDialog(
                '🗑️',
                '清空所有数据',
                '将清除所有传感器的历史数据，此操作不可撤销。确定要继续吗？',
                () => {
                    // 清空数据历史
                    Object.keys(dataHistory).forEach(sensorType => {
                        Object.keys(dataHistory[sensorType]).forEach(nodeKey => {
                            dataHistory[sensorType][nodeKey] = [];
                        });
                    });

                    // 更新图表
                    updateCharts();
                    updateDataStats();

                    console.log('所有数据已清空');
                    showNotification('✅ 所有数据已清空', 'success');
                }
            );
        }

        // 清空当前活跃图表的数据
        function clearCurrentData() {
            if (!currentActiveChart) {
                showNotification('⚠️ 请先点击一个图表', 'warning');
                return;
            }

            const sensorType = currentActiveChart;
            const config = chartConfigs[sensorType];

            showConfirmDialog(
                '🧹',
                '清除当前图表数据',
                `将清除${config.name}传感器的所有历史数据，此操作不可撤销。确定要继续吗？`,
                () => {
                    // 清空指定传感器的数据
                    Object.keys(dataHistory[sensorType]).forEach(nodeKey => {
                        dataHistory[sensorType][nodeKey] = [];
                    });

                    // 更新图表
                    if (charts[sensorType]) {
                        charts[sensorType].updateData(dataHistory[sensorType]);
                    }
                    updateDataStats();

                    console.log(`${config.name}数据已清空`);
                    showNotification(`✅ ${config.name}数据已清空`, 'success');
                }
            );
        }

        // 导出数据
        function exportData() {
            try {
                const exportData = {
                    timestamp: new Date().toISOString(),
                    system: 'IoT传感器监控系统',
                    data: dataHistory
                };

                const jsonStr = JSON.stringify(exportData, null, 2);
                const blob = new Blob([jsonStr], { type: 'application/json' });
                const url = URL.createObjectURL(blob);

                const a = document.createElement('a');
                a.href = url;
                a.download = `iot-sensor-data-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                showNotification('✅ 数据导出成功', 'success');
                console.log('数据导出完成');
            } catch (error) {
                console.error('数据导出失败:', error);
                showNotification('❌ 数据导出失败', 'error');
            }
        }

        // 显示确认对话框
        function showConfirmDialog(icon, title, message, onConfirm) {
            const dialog = document.getElementById('confirmDialog');
            const iconEl = document.getElementById('confirmIcon');
            const titleEl = document.getElementById('confirmTitle');
            const messageEl = document.getElementById('confirmMessage');
            const cancelBtn = document.getElementById('confirmCancel');
            const okBtn = document.getElementById('confirmOk');

            iconEl.textContent = icon;
            titleEl.textContent = title;
            messageEl.textContent = message;

            // 显示对话框
            dialog.classList.add('show');

            // 绑定事件
            const handleCancel = () => {
                dialog.classList.remove('show');
                cancelBtn.removeEventListener('click', handleCancel);
                okBtn.removeEventListener('click', handleConfirm);
            };

            const handleConfirm = () => {
                dialog.classList.remove('show');
                onConfirm();
                cancelBtn.removeEventListener('click', handleCancel);
                okBtn.removeEventListener('click', handleConfirm);
            };

            cancelBtn.addEventListener('click', handleCancel);
            okBtn.addEventListener('click', handleConfirm);

            // 点击背景关闭
            dialog.addEventListener('click', (e) => {
                if (e.target === dialog) {
                    handleCancel();
                }
            });
        }

        // 显示通知
        function showNotification(message, type = 'info') {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 20px;
                border-radius: 8px;
                color: white;
                font-weight: 500;
                z-index: 1001;
                transform: translateX(100%);
                transition: transform 0.3s ease;
                max-width: 300px;
                word-wrap: break-word;
            `;

            // 设置颜色
            switch (type) {
                case 'success':
                    notification.style.background = '#34c759';
                    break;
                case 'warning':
                    notification.style.background = '#ff9500';
                    break;
                case 'error':
                    notification.style.background = '#ff3b30';
                    break;
                default:
                    notification.style.background = '#007aff';
            }

            notification.textContent = message;
            document.body.appendChild(notification);

            // 显示动画
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);

            // 自动隐藏
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }

        // 初始化图表
        function initCharts() {
            const chartsGrid = document.getElementById('chartsGrid');
            chartsGrid.innerHTML = '';

            Object.entries(chartConfigs).forEach(([sensorType, config]) => {
                const chartContainer = createChartContainer(sensorType, config);
                chartsGrid.appendChild(chartContainer);

                // 创建Canvas图表
                const canvas = chartContainer.querySelector('.chart-canvas');
                charts[sensorType] = new SimpleChart(canvas, config, sensorType);
            });
        }

        // 创建图表容器
        function createChartContainer(sensorType, config) {
            const container = document.createElement('div');
            container.className = 'chart-container';
            container.innerHTML = `
                <div class="chart-header">
                    <div class="chart-title">
                        ${config.icon} ${config.name}趋势图
                    </div>
                    <div class="chart-controls">
                        <button class="time-range-btn active" data-range="10">10分钟</button>
                        <button class="time-range-btn" data-range="30">30分钟</button>
                        <button class="time-range-btn" data-range="60">1小时</button>
                    </div>
                </div>
                <canvas class="chart-canvas" width="800" height="300"></canvas>
                <div class="chart-legend">
                    <div class="legend-item">
                        <div class="legend-color" style="background: ${nodeColors.node_1}"></div>
                        <span>节点1</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: ${nodeColors.node_2}"></div>
                        <span>节点2</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: ${nodeColors.node_3}"></div>
                        <span>节点3</span>
                    </div>
                </div>
            `;

            // 绑定时间范围按钮事件
            const buttons = container.querySelectorAll('.time-range-btn');
            buttons.forEach(btn => {
                btn.addEventListener('click', () => {
                    buttons.forEach(b => b.classList.remove('active'));
                    btn.classList.add('active');

                    const range = parseInt(btn.dataset.range);
                    if (charts[sensorType]) {
                        charts[sensorType].setTimeRange(range);
                    }
                });
            });

            // 绑定图表点击事件（用于选择当前活跃图表）
            const canvas = container.querySelector('.chart-canvas');
            const clickHandler = (e) => {
                e.preventDefault();
                e.stopPropagation();

                console.log(`点击了${config.name}图表`);

                // 移除其他图表的活跃状态
                document.querySelectorAll('.chart-container').forEach(c => {
                    c.classList.remove('active-chart');
                });

                // 设置当前图表为活跃状态
                container.classList.add('active-chart');
                currentActiveChart = sensorType;

                // 更新清除按钮状态
                const clearCurrentBtn = document.getElementById('clearCurrentBtn');
                if (clearCurrentBtn) {
                    clearCurrentBtn.innerHTML = `<span>🧹</span><span>清除${config.name}</span>`;
                    clearCurrentBtn.disabled = false;
                }

                showNotification(`已选择${config.name}图表`, 'info');
            };

            // 绑定到整个容器而不只是canvas
            container.addEventListener('click', clickHandler);
            canvas.addEventListener('click', clickHandler);

            return container;
        }

        // 更新所有图表
        function updateCharts() {
            Object.keys(charts).forEach(sensorType => {
                if (charts[sensorType]) {
                    charts[sensorType].updateData(dataHistory[sensorType]);
                }
            });
        }

        // 简单图表类
        class SimpleChart {
            constructor(canvas, config, sensorType) {
                this.canvas = canvas;
                this.ctx = canvas.getContext('2d');
                this.config = config;
                this.sensorType = sensorType;
                this.timeRange = 10; // 默认10分钟
                this.data = {};

                // 设置Canvas尺寸
                this.setupCanvas();
            }

            setupCanvas() {
                // 确保canvas有固定尺寸
                const containerWidth = this.canvas.parentElement.clientWidth - 40; // 减去padding
                const containerHeight = 300;

                const dpr = window.devicePixelRatio || 1;

                this.canvas.width = containerWidth * dpr;
                this.canvas.height = containerHeight * dpr;

                this.ctx.scale(dpr, dpr);
                this.canvas.style.width = containerWidth + 'px';
                this.canvas.style.height = containerHeight + 'px';

                console.log(`Canvas设置: ${containerWidth}x${containerHeight}, DPR: ${dpr}`);
            }

            setTimeRange(minutes) {
                this.timeRange = minutes;
                this.updateData(this.data);
            }

            updateData(data) {
                this.data = data;
                this.draw();
            }

            draw() {
                const ctx = this.ctx;
                const canvas = this.canvas;
                const width = canvas.width / (window.devicePixelRatio || 1);
                const height = canvas.height / (window.devicePixelRatio || 1);

                // 清空画布
                ctx.clearRect(0, 0, width, height);

                // 绘制背景
                ctx.fillStyle = '#ffffff';
                ctx.fillRect(0, 0, width, height);

                // 绘制网格
                this.drawGrid(ctx, width, height);

                // 绘制数据线
                this.drawDataLines(ctx, width, height);

                // 绘制坐标轴标签
                this.drawLabels(ctx, width, height);
            }

            drawGrid(ctx, width, height) {
                const padding = 60;
                const gridColor = '#e5e5ea';

                ctx.strokeStyle = gridColor;
                ctx.lineWidth = 1;

                // 垂直网格线
                for (let i = 0; i <= 10; i++) {
                    const x = padding + (width - padding * 2) * i / 10;
                    ctx.beginPath();
                    ctx.moveTo(x, padding);
                    ctx.lineTo(x, height - padding);
                    ctx.stroke();
                }

                // 水平网格线
                for (let i = 0; i <= 5; i++) {
                    const y = padding + (height - padding * 2) * i / 5;
                    ctx.beginPath();
                    ctx.moveTo(padding, y);
                    ctx.lineTo(width - padding, y);
                    ctx.stroke();
                }
            }

            drawDataLines(ctx, width, height) {
                const padding = 60;
                const chartWidth = width - padding * 2;
                const chartHeight = height - padding * 2;

                const now = new Date();
                const timeRangeMs = this.timeRange * 60 * 1000;
                const startTime = new Date(now.getTime() - timeRangeMs);

                // 调试信息
                if (this.sensorType === 'temperature') {
                    console.log(`绘制${this.config.name}图表:`, {
                        dataKeys: Object.keys(this.data),
                        timeRange: this.timeRange,
                        configRange: `${this.config.min}-${this.config.max}${this.config.unit}`
                    });
                }

                Object.entries(this.data).forEach(([nodeKey, nodeData]) => {
                    if (!nodeData || nodeData.length === 0) {
                        if (this.sensorType === 'temperature') {
                            console.log(`${nodeKey}: 无数据`);
                        }
                        return;
                    }

                    // 过滤时间范围内的数据
                    const filteredData = nodeData.filter(point => {
                        const pointTime = new Date(point.time);
                        return pointTime >= startTime && pointTime <= now;
                    });

                    if (filteredData.length < 1) {
                        if (this.sensorType === 'temperature') {
                            console.log(`${nodeKey}: 时间范围内无数据`);
                        }
                        return;
                    }

                    // 调试信息
                    if (this.sensorType === 'temperature') {
                        console.log(`${nodeKey}: ${filteredData.length}个数据点`,
                            filteredData.map(p => `${p.value}${this.config.unit}`).slice(0, 3));
                    }

                    // 设置线条样式
                    ctx.strokeStyle = nodeColors[nodeKey] || '#999999';
                    ctx.lineWidth = 2;
                    ctx.lineCap = 'round';
                    ctx.lineJoin = 'round';
                    ctx.beginPath();

                    let validPoints = 0;
                    filteredData.forEach((point, index) => {
                        const timeProgress = (new Date(point.time) - startTime) / timeRangeMs;
                        // 使用0作为最小值进行计算
                        const valueProgress = point.value / this.config.max;

                        // 确保数值在有效范围内
                        const clampedTimeProgress = Math.max(0, Math.min(1, timeProgress));
                        const clampedValueProgress = Math.max(0, Math.min(1, valueProgress));

                        const x = padding + chartWidth * clampedTimeProgress;
                        const y = padding + chartHeight * (1 - clampedValueProgress);

                        // 检查坐标是否有效
                        if (isFinite(x) && isFinite(y)) {
                            if (validPoints === 0) {
                                ctx.moveTo(x, y);
                            } else {
                                ctx.lineTo(x, y);
                            }
                            validPoints++;
                        }
                    });

                    if (validPoints > 0) {
                        ctx.stroke();

                        // 绘制数据点
                        ctx.fillStyle = nodeColors[nodeKey] || '#999999';
                        filteredData.forEach(point => {
                            const timeProgress = (new Date(point.time) - startTime) / timeRangeMs;
                            // 使用0作为最小值进行计算
                            const valueProgress = point.value / this.config.max;

                            const clampedTimeProgress = Math.max(0, Math.min(1, timeProgress));
                            const clampedValueProgress = Math.max(0, Math.min(1, valueProgress));

                            const x = padding + chartWidth * clampedTimeProgress;
                            const y = padding + chartHeight * (1 - clampedValueProgress);

                            if (isFinite(x) && isFinite(y)) {
                                ctx.beginPath();
                                ctx.arc(x, y, 3, 0, Math.PI * 2);
                                ctx.fill();
                            }
                        });
                    }
                });
            }

            drawLabels(ctx, width, height) {
                const padding = 60;

                ctx.fillStyle = '#8e8e93';
                ctx.font = '12px -apple-system, BlinkMacSystemFont, sans-serif';
                ctx.textAlign = 'center';

                // Y轴标签（数值） - 确保从0开始
                for (let i = 0; i <= 5; i++) {
                    // 从最大值到0的刻度
                    const value = this.config.max * (1 - i / 5);
                    const y = padding + (height - padding * 2) * i / 5;

                    ctx.textAlign = 'right';
                    // 根据传感器类型调整小数位数
                    const decimals = this.sensorType === 'temperature' ? 1 : 0;
                    ctx.fillText(value.toFixed(decimals) + this.config.unit, padding - 10, y + 4);
                }

                // X轴标签（时间）
                const now = new Date();
                for (let i = 0; i <= 5; i++) {
                    const timeOffset = this.timeRange * 60 * 1000 * (1 - i / 5);
                    const time = new Date(now.getTime() - timeOffset);
                    const x = padding + (width - padding * 2) * i / 5;

                    ctx.textAlign = 'center';
                    ctx.fillText(time.toLocaleTimeString().slice(0, 5), x, height - padding + 20);
                }
            }
        }

        // 重新绘制温度趋势图
        function redrawTemperatureChart() {
            console.log('重新绘制温度趋势图...');

            // 清空温度数据
            if (dataHistory.temperature) {
                Object.keys(dataHistory.temperature).forEach(nodeKey => {
                    dataHistory.temperature[nodeKey] = [];
                });
            }

            // 重新生成温度初始数据
            const now = new Date();
            const pointCount = 20;

            for (let i = pointCount; i >= 0; i--) {
                const timestamp = new Date(now.getTime() - i * 30000);

                Object.keys(dataHistory.temperature).forEach(nodeKey => {
                    let value = 0;

                    if (nodeKey === 'node_1') {
                        value = 25 + 8 * Math.sin(i * 0.1) + 3 * (Math.random() - 0.5);
                        value = Math.max(18, Math.min(35, value));
                    }

                    dataHistory.temperature[nodeKey].push({
                        time: timestamp,
                        value: parseFloat(value.toFixed(1))
                    });
                });
            }

            // 更新温度图表
            if (charts.temperature) {
                charts.temperature.updateData(dataHistory.temperature);
            }

            console.log('温度趋势图重新绘制完成');
        }

        // 页面加载完成后开始获取数据
        document.addEventListener('DOMContentLoaded', () => {
            console.log('页面加载完成，开始获取数据');

            // 1. 生成初始历史数据
            generateInitialData();

            // 2. 初始化图表
            initCharts();

            // 3. 重新绘制温度图表（确保Y轴从0开始）
            setTimeout(() => {
                redrawTemperatureChart();
            }, 500);

            // 4. 更新图表显示初始数据
            updateCharts();
            updateDataStats();

            // 5. 绑定控制按钮事件
            bindControlEvents();

            // 6. 开始获取实时数据
            loadData();

            // 7. 每10秒更新一次数据
            setInterval(loadData, 10000);
        });

        // 绑定控制按钮事件
        function bindControlEvents() {
            // 清空所有数据按钮
            const clearAllBtn = document.getElementById('clearAllBtn');
            if (clearAllBtn) {
                clearAllBtn.addEventListener('click', clearAllData);
            }

            // 清除当前图表数据按钮
            const clearCurrentBtn = document.getElementById('clearCurrentBtn');
            if (clearCurrentBtn) {
                clearCurrentBtn.addEventListener('click', clearCurrentData);
                clearCurrentBtn.disabled = true; // 初始状态禁用
            }

            // 导出数据按钮
            const exportDataBtn = document.getElementById('exportDataBtn');
            if (exportDataBtn) {
                exportDataBtn.addEventListener('click', exportData);
            }
        }
    </script>
</body>
</html>
