// API调用封装
import { CONFIG, Logger } from './config.js';

class ApiClient {
  constructor() {
    this.baseUrl = CONFIG.API_BASE_URL;
    this.retryCount = 0;
  }

  async request(endpoint, options = {}) {
    const url = `${this.baseUrl}${endpoint}`;
    const defaultOptions = {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      ...options
    };

    try {
      Logger.log(`API请求: ${url}`);
      const response = await fetch(url, defaultOptions);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      this.retryCount = 0; // 重置重试计数
      return { success: true, data };
      
    } catch (error) {
      Logger.error(`API请求失败: ${url}`, error);
      return { success: false, error: error.message };
    }
  }

  async getSensorsData() {
    return await this.request(CONFIG.API_ENDPOINTS.SENSORS);
  }
}

// 导出单例
export const apiClient = new ApiClient();
