#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
开发环境快速启动脚本
分离式启动前后端服务，便于开发调试
"""

import sys
import os
import time
import threading
import subprocess
import webbrowser
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def print_dev_banner():
    """打印开发模式横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    IoT传感器监控系统                          ║
║                      开发调试模式                            ║
╠══════════════════════════════════════════════════════════════╣
║  🔧 模式: 分离式开发                                          ║
║  📊 后端API: http://127.0.0.1:5000                          ║
║  🌐 前端页面: http://127.0.0.1:8000/frontend/index.html      ║
║  🛠️ 适用于: 前后端独立开发调试                                ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def start_backend():
    """启动后端API服务"""
    print("🚀 启动后端API服务...")
    try:
        from backend.app import main
        main()
    except KeyboardInterrupt:
        print("后端服务已停止")
    except Exception as e:
        print(f"后端服务启动失败: {e}")

def start_frontend():
    """启动前端开发服务器"""
    print("🌐 启动前端开发服务器...")
    try:
        subprocess.run([
            sys.executable, "-m", "http.server", "8000"
        ], cwd=project_root)
    except KeyboardInterrupt:
        print("前端服务已停止")
    except Exception as e:
        print(f"前端服务启动失败: {e}")

def open_dev_pages():
    """打开开发页面"""
    time.sleep(3)  # 等待服务启动
    
    try:
        # 打开API文档页面
        webbrowser.open('http://127.0.0.1:5000')
        time.sleep(1)
        
        # 打开前端页面
        webbrowser.open('http://127.0.0.1:8000/frontend/index.html')
        
        print("🌐 已在浏览器中打开开发页面")
    except:
        print("ℹ️ 请手动在浏览器中访问:")
        print("   后端API: http://127.0.0.1:5000")
        print("   前端页面: http://127.0.0.1:8000/frontend/index.html")

def main():
    """主函数"""
    print_dev_banner()
    
    print("🔍 开发模式说明:")
    print("  - 后端和前端分离运行，便于独立开发")
    print("  - 后端修改后需要重启服务")
    print("  - 前端修改后刷新浏览器即可")
    print("  - 按 Ctrl+C 停止所有服务")
    print("\n" + "="*60)
    
    # 启动浏览器线程
    browser_thread = threading.Thread(target=open_dev_pages, daemon=True)
    browser_thread.start()
    
    # 启动后端线程
    backend_thread = threading.Thread(target=start_backend, daemon=True)
    backend_thread.start()
    
    # 等待后端启动
    time.sleep(2)
    
    try:
        # 主线程运行前端服务
        start_frontend()
    except KeyboardInterrupt:
        print("\n👋 开发服务已停止")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 用户中断，程序退出")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 程序异常退出: {e}")
        sys.exit(1)
