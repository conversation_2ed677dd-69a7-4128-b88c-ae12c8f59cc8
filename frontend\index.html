<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="华为云IoT传感器数据实时监控系统">
    <title>IoT传感器监控 - 华为云</title>
    
    <!-- CSS样式文件 -->
    <link rel="stylesheet" href="css/variables.css">
    <link rel="stylesheet" href="css/base.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/animations.css">
    
    <!-- 预加载关键资源 -->
    <link rel="preload" href="js/config.js" as="script">
    <link rel="preload" href="js/api.js" as="script">
</head>
<body>
    <!-- 主容器 -->
    <div class="app-container">
        <!-- 头部区域 -->
        <header class="app-header">
            <div class="header-content">
                <h1 class="app-title">
                    <span class="title-icon">📊</span>
                    IoT传感器监控
                </h1>
                <div class="status-indicator">
                    <span class="status-dot" id="connectionStatus"></span>
                    <span class="status-text" id="statusText">连接中...</span>
                </div>
            </div>
        </header>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 数据概览卡片 -->
            <section class="overview-section">
                <div class="section-header">
                    <h2 class="section-title">实时数据概览</h2>
                    <div class="last-update">
                        <span>最后更新：</span>
                        <time id="lastUpdateTime">--</time>
                    </div>
                </div>
            </section>

            <!-- 传感器节点网格 -->
            <section class="nodes-grid" id="nodesGrid">
                <!-- 节点卡片将通过JavaScript动态生成 -->
                <div class="loading-placeholder">
                    <div class="loading-spinner"></div>
                    <p>正在加载传感器数据...</p>
                </div>
            </section>

            <!-- 错误提示区域 -->
            <section class="error-section" id="errorSection" style="display: none;">
                <div class="error-card">
                    <div class="error-icon">⚠️</div>
                    <h3 class="error-title">数据获取失败</h3>
                    <p class="error-message" id="errorMessage">请检查网络连接或稍后重试</p>
                    <button class="retry-button" id="retryButton">重新加载</button>
                </div>
            </section>
        </main>

        <!-- 底部信息 -->
        <footer class="app-footer">
            <div class="footer-content">
                <p class="footer-text">
                    华为云IoT传感器监控系统 | 
                    <span class="footer-version">v1.0.0</span>
                </p>
                <div class="footer-links">
                    <a href="#" class="footer-link">帮助</a>
                    <a href="#" class="footer-link">关于</a>
                </div>
            </div>
        </footer>
    </div>

    <!-- JavaScript模块 -->
    <script type="module" src="js/config.js"></script>
    <script type="module" src="js/api.js"></script>
    <script type="module" src="js/components.js"></script>
    <script type="module" src="js/animations.js"></script>
    <script type="module" src="js/main.js"></script>
</body>
</html>
